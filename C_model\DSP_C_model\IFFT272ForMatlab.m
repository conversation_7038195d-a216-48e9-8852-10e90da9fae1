%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Declaration :         [output_real, output_imag, bitshift_num] = IFFT272ForMatlab(input_real, input_imag)
% function :            272 points ifft
% input:                input_real    -   real part of IFFT input, 1*272, register value, Q(16,1)
%                       input_imag    -   imaginary part of IFFT input, 1*272, register value, Q(16,1)
% output:               output_real   -   real part of IFFT output 1*272 16bits 
%                       output_imag   -   imaginary part of IFFT output 1*272 16bits 
%                       bitshift_num  -   right bitshift num in IFFT process
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

