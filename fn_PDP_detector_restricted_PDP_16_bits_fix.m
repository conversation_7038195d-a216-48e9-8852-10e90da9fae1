%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_PDP_detector_restricted_fix
%
% Function:     access detector for high_speed mode
%
%
% Input:        PDP_matrix_fix                         - PDP
%               PRACH_restricted_set_cfg               - restricted set configration, 0-unrestricted 1-restricted type A 2-restricted type B, higher-layer parameter
%               PRACH_Ncs                              - PRACH Ncs
%               PRACH_Cv                               - Cv, please refer to 3PGG TS 38.211
%               PRACH_du                               - du, please refer to 3PGG TS 38.211
%
% Output:       TA_matrix_fix                          - TA
%
% Modification History
% --------------------
% Date      20180201
% Author    WANG Jinguo
% Content   Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function [RA_status_matrix, preamble_power_matrix_fix, TA_result_fix] = fn_PDP_detector_restricted_PDP_16_bits_fix(PDP_matrix_fix, PRACH_restricted_set_cfg, PRACH_Ncs, PRACH_Cv, PRACH_du, detecting_threshold_fix, plot_PDP_enable)
fn_fix_point_format_table;

detecting_threshold_fix     = fi(detecting_threshold_fix, SW16F09, F_floor);

[basic_seq_num, PDP_len]    = size(PDP_matrix_fix);
Cv_len                      = length(PRACH_Cv);
win_idx_list                = 0:1:(Cv_len-1);

inv_noise_len_list_fix      = fi(1./[1:839], SW16F12, F_floor);

RA_status_matrix            = zeros(1,64);
preamble_power_matrix_fix   = fi(zeros(1,64), SW32F28, F_floor);
TA_result_fix               = fi(-1*ones(1,64), SW16F04, F_floor);

V_threshold_fix             = fi(0.5, SW16F04, F_floor);
alpha_fix                   = fi(0.5, TA_result_fix.numerictype, F_floor);

%% generate detector win
% Center win
win_start_idx_list    = mod(PDP_len - PRACH_Cv, PDP_len);
win_stop_idx_list     = win_start_idx_list + PRACH_Ncs - 1;

%Left win
L_win_start_idx_list  = mod(win_start_idx_list-PRACH_du, PDP_len);
L_win_stop_idx_list   = L_win_start_idx_list + PRACH_Ncs - 1;

%Right win
R_win_start_idx_list  = mod(win_start_idx_list+PRACH_du, PDP_len);
R_win_stop_idx_list   = R_win_start_idx_list + PRACH_Ncs -1;

win_num               = length(win_start_idx_list);

if PRACH_restricted_set_cfg == 2
    %% generate another two detector win for Type B
    %Left2 win
    L2_win_start_idx_list  = mod(win_start_idx_list-2*PRACH_du, PDP_len);
    L2_win_stop_idx_list   = L2_win_start_idx_list + PRACH_Ncs - 1;
    
    %Right win
    R2_win_start_idx_list  = mod(win_start_idx_list+2*PRACH_du, PDP_len);
    R2_win_stop_idx_list   = R2_win_start_idx_list + PRACH_Ncs -1;
end

if PRACH_restricted_set_cfg == 1
    for basic_seq_idx = 0:1:(basic_seq_num-1)
        if plot_PDP_enable == 1
            figure
            plot_int_PDP_current_seq = fn_fi2double(PDP_matrix_fix(basic_seq_idx+1, :));
            plot_PDP_current_seq_len = length(plot_int_PDP_current_seq);
            x_list = 0:1:(plot_PDP_current_seq_len-1);
            plot(x_list, plot_int_PDP_current_seq);
            hold on;
            grid on;
            
            plot_win_boundary = 0*ones(1,plot_PDP_current_seq_len);
            for win_idx = 0:1:(win_num-1)
                win_start_idx            = win_start_idx_list(win_idx+1);
                win_stop_idx             = win_stop_idx_list(win_idx+1);
                plot_win_boundary((win_start_idx+1):(win_stop_idx+1)) = -max(plot_int_PDP_current_seq)*0.15;
                plot_win_boundary((win_start_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
                plot_win_boundary((win_stop_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
            end
            plot(x_list,plot_win_boundary, '--r');
            
            plot_win_boundary = 0*ones(1,plot_PDP_current_seq_len);
            for win_idx = 0:1:(win_num-1)
                win_start_idx            = L_win_start_idx_list(win_idx+1);
                win_stop_idx             = L_win_stop_idx_list(win_idx+1);
                plot_win_boundary((win_start_idx+1):(win_stop_idx+1)) = -max(plot_int_PDP_current_seq)*0.15;
                plot_win_boundary((win_start_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
                plot_win_boundary((win_stop_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
            end
            plot(x_list,plot_win_boundary, '--g');
            
            plot_win_boundary = 0*ones(1,plot_PDP_current_seq_len);
            for win_idx = 0:1:(win_num-1)
                win_start_idx            = R_win_start_idx_list(win_idx+1);
                win_stop_idx             = R_win_stop_idx_list(win_idx+1);
                plot_win_boundary((win_start_idx+1):(win_stop_idx+1)) = -max(plot_int_PDP_current_seq)*0.15;
                plot_win_boundary((win_start_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
                plot_win_boundary((win_stop_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
            end
            plot(x_list,plot_win_boundary, '--k');
            
            title(strcat('PDP of sequence ', num2str(basic_seq_idx)));
            legend('PDP','center window','left window','right window');
        end
        
        peak_pos_per_win                     = zeros(3*win_num, 4);                                  % save peak pos
        peak_value_final_per_win_fix         = fi(zeros(win_num, 1), SW16F14, F_floor);            % save combining peak value
        alpha_sign_per_win                   = zeros(win_num, 1);                                  % save direction of TA adjustment
        p_for_TA_adjust_enable_per_win       = fi(zeros(win_num, 1), SW16F14, F_floor);
        peak_pos_final_all                   = zeros(win_num, 1);
        peak_org_value_per_win_fix           = fi(zeros(win_num, 1), SW16F14, F_floor); 
        
        for win_idx = 0:1:(win_num-1)
            %% read samples of center window
            win_start_idx            = win_start_idx_list(win_idx+1);
            win_stop_idx             = win_stop_idx_list(win_idx+1);
            win_samples_fix          = fi(PDP_matrix_fix(basic_seq_idx+1, (win_start_idx+1):(win_stop_idx+1)), SW16F15, F_floor);
            
            %% read samples of left window
            L_win_start_idx          = L_win_start_idx_list(win_idx+1);
            L_win_stop_idx           = L_win_stop_idx_list(win_idx+1);
            L_win_samples_fix        = fi(PDP_matrix_fix(basic_seq_idx+1, (L_win_start_idx+1):(L_win_stop_idx+1)), SW16F15, F_floor);
            
            %% read samples of right window
            R_win_start_idx          = R_win_start_idx_list(win_idx+1);
            R_win_stop_idx           = R_win_stop_idx_list(win_idx+1);
            R_win_samples_fix        = fi(PDP_matrix_fix(basic_seq_idx+1, (R_win_start_idx+1):(R_win_stop_idx+1)), SW16F15, F_floor);
            
            win_len                  = length(win_samples_fix);
            
            %% calculate combining peak value and noise value
            %% sort of center window
            [tmp, descend_index]     = sort(double(win_samples_fix), 2, 'descend');
            peak_pos                 = descend_index(1);
            peak_value_fix           = win_samples_fix(peak_pos);
            
            %% sort of left window
            [L_tmp, L_descend_index]     = sort(double(L_win_samples_fix), 2, 'descend');
            L_peak_pos                   = L_descend_index(1);
            L_peak_value_fix             = L_win_samples_fix(L_peak_pos);
            
            %% sort of right window
            [R_tmp, R_descend_index]     = sort(double(R_win_samples_fix), 2, 'descend');
            R_peak_pos                   = R_descend_index(1);
            R_peak_value_fix             = R_win_samples_fix(R_peak_pos);
            
            peak_pos_per_win(win_idx*3+1,:)  = win_start_idx + descend_index(1:4);
            peak_pos_per_win(win_idx*3+2,:)  = L_win_start_idx + L_descend_index(1:4);
            peak_pos_per_win(win_idx*3+3,:)  = R_win_start_idx + R_descend_index(1:4);
            
            %% center window peak value
            if peak_pos == 1
                peak_value_tmp = fi(peak_value_fix + win_samples_fix(peak_pos+1), SW16F14, F_floor);
            elseif peak_pos == win_len
                peak_value_tmp = fi(peak_value_fix + win_samples_fix(peak_pos-1), SW16F14, F_floor);
            else
                peak_value_tmp = fi(peak_value_fix + win_samples_fix(peak_pos-1) + win_samples_fix(peak_pos+1), SW16F14, F_floor);
            end
            
            %% left window peak value
            if L_peak_pos == 1
                L_peak_value_tmp = fi(L_peak_value_fix + L_win_samples_fix(L_peak_pos+1), SW16F14, F_floor);
            elseif L_peak_pos == win_len
                L_peak_value_tmp = fi(L_peak_value_fix + L_win_samples_fix(L_peak_pos-1), SW16F14, F_floor);
            else
                L_peak_value_tmp = fi(L_peak_value_fix + L_win_samples_fix(L_peak_pos-1) + L_win_samples_fix(L_peak_pos+1), SW16F14, F_floor);
            end
            
            %% right window peak value
            if R_peak_pos == 1
                R_peak_value_tmp = fi(R_peak_value_fix + R_win_samples_fix(R_peak_pos+1), SW16F14, F_floor);
            elseif R_peak_pos == win_len
                R_peak_value_tmp = fi(R_peak_value_fix + R_win_samples_fix(R_peak_pos-1), SW16F14, F_floor);
            else
                R_peak_value_tmp = fi(R_peak_value_fix + R_win_samples_fix(R_peak_pos-1) + R_win_samples_fix(R_peak_pos+1), SW16F14, F_floor);
            end
            
            %% get max peak_value of center window\left window\right window
            peak_tmp = [peak_value_tmp, L_peak_value_tmp, R_peak_value_tmp];
            max_peak_value = max(peak_tmp);
            
            %% get final peak value and peak pos
           if max_peak_value == peak_value_tmp
                peak_value_final_fix  = peak_value_tmp;
                peak_pos_final        = peak_pos;
                peak_org_fix          = peak_value_fix;
            elseif max_peak_value == L_peak_value_tmp
                peak_value_final_fix  = L_peak_value_tmp;
                peak_pos_final        = L_peak_pos;
                peak_org_fix          = L_peak_value_fix;
            else
                peak_value_final_fix  = R_peak_value_tmp;
                peak_pos_final        = R_peak_pos;
                peak_org_fix          = R_peak_value_fix;
            end
            
            peak_pos_final_all(win_idx+1, :)           = peak_pos_final;
            peak_value_final_per_win_fix(win_idx+1, :) = peak_value_final_fix;
            peak_org_value_per_win_fix(win_idx+1, :)   = fi(peak_org_fix, SW16F14, F_floor);
            
            %% save direction of TA adjustment            
            if (peak_pos_final == 0+1)
                P_right_fix   = win_samples_fix(peak_pos_final+1);
                P_left_fix    = fi(0, SW16F15, F_floor);
            elseif (peak_pos_final == (PRACH_Ncs - 1 + 1))
                P_right_fix   = fi(0, SW16F15, F_floor);
                P_left_fix    = win_samples_fix(peak_pos_final-1);
            else
                P_right_fix   = win_samples_fix(peak_pos_final+1);
                P_left_fix    = win_samples_fix(peak_pos_final-1);
            end
            
            if P_right_fix>=P_left_fix
                P_fix = P_right_fix;
                alpha_sign = 1;
            else
                P_fix = P_left_fix;
                alpha_sign = -1;
            end
            
            alpha_sign_per_win(win_idx+1, :)                 = alpha_sign;
            p_for_TA_adjust_enable_per_win(win_idx+1, :)     = fi(P_fix, SW16F14, F_floor);
            
        end %window loop
        
        %% noise value
        peak_pos_idx             = reshape(peak_pos_per_win, 1, []);
        noise_idx                = setdiff((1:1:size(PDP_matrix_fix,2)), peak_pos_idx);
        noise_sample_fix         = fi(PDP_matrix_fix(basic_seq_idx+1, noise_idx), SW16F15, F_floor);
        noise_sample_num         = length(noise_sample_fix);
        noise_sample_shift_fix   = fi(noise_sample_fix, SW16F15, F_floor);
        noise_sample_sum_fix     = fi(sum(noise_sample_shift_fix), SW32F15, F_floor);
        inv_noise_len_fix        = inv_noise_len_list_fix(noise_sample_num);
        noise_avg_fix            = fi(noise_sample_sum_fix*inv_noise_len_fix, SW16F15, F_floor);
        int_noise_avg_fix        = fn_fi2double(noise_avg_fix);
        if int_noise_avg_fix == 0
            noise_avg_fix = fi(5/(2^30),SW16F15, F_floor);
        end
        
        ref_fix = fi(noise_avg_fix*detecting_threshold_fix, SW16F14, F_floor);
        
        for win_idx = 0:1:(win_num-1)
            peak_value_final_fix = peak_value_final_per_win_fix(win_idx+1, :);
            peak_org_fix         = peak_org_value_per_win_fix(win_idx+1, :);
            alpha_sign           = alpha_sign_per_win(win_idx+1, :);
            peak_pos             = peak_pos_final_all(win_idx+1, :) - 1;
            P_fix                = p_for_TA_adjust_enable_per_win(win_idx+1, :);
            preamble_ID          = basic_seq_idx*win_num + win_idx;
            
            switch detecting_mode
                case 1
                    peak_value_current_mode_fix = peak_value_final_fix;
                case 2
                    peak_value_current_mode_fix = peak_value_fix;
            end

            if peak_value_final_fix > ref_fix
                RA_status_matrix(preamble_ID+1) = 1;
                %% measure preamble power
                
                %% TA
                ref_alpha_fix = fi(P_fix*V_threshold_fix, SW16F14, F_floor);
                if peak_org_fix<ref_alpha_fix
                    TA_current = fi(peak_pos, TA_result_fix.numerictype, F_floor) + alpha_sign*alpha_fix;
                else
                    TA_current = fi(peak_pos, TA_result_fix.numerictype, F_floor);
                end
                TA_current_fix = fi(TA_current, TA_result_fix.numerictype, F_floor);
                
                TA_result_fix(preamble_ID+1) = TA_current_fix;
            else
                RA_status_matrix(basic_seq_idx*win_num + win_idx + 1) = 0;
            end
        end % window loop
        
    end %basic sequence loop
elseif PRACH_restricted_set_cfg == 2
    for basic_seq_idx = 0:1:(basic_seq_num-1)
        if plot_PDP_enable == 1
            figure
            plot_int_PDP_current_seq = fn_fi2double(PDP_matrix_fix(basic_seq_idx+1, :));
            plot_PDP_current_seq_len = length(plot_int_PDP_current_seq);
            x_list = 0:1:(plot_PDP_current_seq_len-1);
            plot(x_list, plot_int_PDP_current_seq);
            hold on;
            grid on;
            plot_win_boundary = zeros(1,plot_PDP_current_seq_len);
            plot_win_boundary(win_start_idx_list+1) = max(plot_int_PDP_current_seq)*0.5;
            plot_win_boundary(win_stop_idx_list+1) = max(plot_int_PDP_current_seq)*0.5;
            stem(x_list,plot_win_boundary, 'r');
            
            plot_win_boundary = 0*ones(1,plot_PDP_current_seq_len);
            for win_idx = 0:1:(win_num-1)
                win_start_idx            = L_win_start_idx_list(win_idx+1);
                win_stop_idx             = L_win_stop_idx_list(win_idx+1);
                plot_win_boundary((win_start_idx+1):(win_stop_idx+1)) = -max(plot_int_PDP_current_seq)*0.15;
                plot_win_boundary((win_start_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
                plot_win_boundary((win_stop_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
            end
            plot(x_list,plot_win_boundary, '--g');
            
            plot_win_boundary = 0*ones(1,plot_PDP_current_seq_len);
            for win_idx = 0:1:(win_num-1)
                win_start_idx            = R_win_start_idx_list(win_idx+1);
                win_stop_idx             = R_win_stop_idx_list(win_idx+1);
                plot_win_boundary((win_start_idx+1):(win_stop_idx+1)) = -max(plot_int_PDP_current_seq)*0.15;
                plot_win_boundary((win_start_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
                plot_win_boundary((win_stop_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
            end
            plot(x_list,plot_win_boundary, '--k');
            
            plot_win_boundary = 0*ones(1,plot_PDP_current_seq_len);
            for win_idx = 0:1:(win_num-1)
                win_start_idx            = L2_win_start_idx_list(win_idx+1);
                win_stop_idx             = L2_win_stop_idx_list(win_idx+1);
                plot_win_boundary((win_start_idx+1):(win_stop_idx+1)) = -max(plot_int_PDP_current_seq)*0.15;
                plot_win_boundary((win_start_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
                plot_win_boundary((win_stop_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
            end
            plot(x_list,plot_win_boundary, '--*g');
            
            plot_win_boundary = 0*ones(1,plot_PDP_current_seq_len);
            for win_idx = 0:1:(win_num-1)
                win_start_idx            = R2_win_start_idx_list(win_idx+1);
                win_stop_idx             = R2_win_stop_idx_list(win_idx+1);
                plot_win_boundary((win_start_idx+1):(win_stop_idx+1)) = -max(plot_int_PDP_current_seq)*0.15;
                plot_win_boundary((win_start_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
                plot_win_boundary((win_stop_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
            end
            plot(x_list,plot_win_boundary, '--*k');
            
            title(strcat('PDP of sequence ', num2str(basic_seq_idx)));
            legend('PDP','center window','left window 1','right window 1','left window 2','right window 2');
        end
        
        peak_pos_per_win                     = zeros(5*win_num, 4);                                % save peak pos
        peak_value_final_per_win_fix         = fi(zeros(win_num, 1), SW16F14, F_floor);            % save combining peak value
        alpha_sign_per_win                   = zeros(win_num, 1);                                  % save direction of TA adjustment
        p_for_TA_adjust_enable_per_win       = fi(zeros(win_num, 1), SW16F14, F_floor);
        peak_pos_final_all                   = zeros(win_num, 1);
        peak_org_value_per_win_fix           = fi(zeros(win_num, 1), SW16F14, F_floor);
        
        for win_idx = 0:1:(win_num-1)
            %% read samples of center window
            win_start_idx            = win_start_idx_list(win_idx+1);
            win_stop_idx             = win_stop_idx_list(win_idx+1);
            win_samples_fix          = fi(PDP_matrix_fix(basic_seq_idx+1, (win_start_idx+1):(win_stop_idx+1)), SW16F15, F_floor);
            
            %% read samples of left1 window
            L_win_start_idx          = L_win_start_idx_list(win_idx+1);
            L_win_stop_idx           = L_win_stop_idx_list(win_idx+1);
            L_win_samples_fix        = fi(PDP_matrix_fix(basic_seq_idx+1, (L_win_start_idx+1):(L_win_stop_idx+1)), SW16F15, F_floor);
            
            %% read samples of right1 window
            R_win_start_idx          = R_win_start_idx_list(win_idx+1);
            R_win_stop_idx           = R_win_stop_idx_list(win_idx+1);
            R_win_samples_fix        = fi(PDP_matrix_fix(basic_seq_idx+1, (R_win_start_idx+1):(R_win_stop_idx+1)), SW16F15, F_floor);
            
            %% read samples of left2 window
            L2_win_start_idx         = L2_win_start_idx_list(win_idx+1);
            L2_win_stop_idx          = L2_win_stop_idx_list(win_idx+1);
            L2_win_samples_fix       = fi(PDP_matrix_fix(basic_seq_idx+1, (L2_win_start_idx+1):(L2_win_stop_idx+1)), SW16F15, F_floor);
            
            %% read samples of right2 window
            R2_win_start_idx         = R2_win_start_idx_list(win_idx+1);
            R2_win_stop_idx          = R2_win_stop_idx_list(win_idx+1);
            R2_win_samples_fix       = fi(PDP_matrix_fix(basic_seq_idx+1, (R2_win_start_idx+1):(R2_win_stop_idx+1)), SW16F15, F_floor);
            
            win_len                  = length(win_samples_fix);
            
            %% calculate combining peak value and noise value
            %% sort of center window
            [tmp, descend_index]     = sort(double(win_samples_fix), 2, 'descend');
            peak_pos                 = descend_index(1);
            peak_value_fix           = win_samples_fix(peak_pos);
            
            %% sort of left1 window
            [L_tmp, L_descend_index]     = sort(double(L_win_samples_fix), 2, 'descend');
            L_peak_pos                   = L_descend_index(1);
            L_peak_value_fix             = L_win_samples_fix(L_peak_pos);
            
            %% sort of right1 window
            [R_tmp, R_descend_index]     = sort(double(R_win_samples_fix), 2, 'descend');
            R_peak_pos                   = R_descend_index(1);
            R_peak_value_fix             = R_win_samples_fix(R_peak_pos);
            
            %% sort of left2 window
            [L2_tmp, L2_descend_index]     = sort(double(L2_win_samples_fix), 2, 'descend');
            L2_peak_pos                   = L2_descend_index(1);
            L2_peak_value_fix             = L2_win_samples_fix(L2_peak_pos);
            
            %% sort of right2 window
            [R2_tmp, R2_descend_index]     = sort(double(R2_win_samples_fix), 2, 'descend');
            R2_peak_pos                   = R2_descend_index(1);
            R2_peak_value_fix             = R2_win_samples_fix(R2_peak_pos);
            
            peak_pos_per_win(win_idx*5+1,:)  = win_start_idx + descend_index(1:4);
            peak_pos_per_win(win_idx*5+2,:)  = L_win_start_idx + L_descend_index(1:4);
            peak_pos_per_win(win_idx*5+3,:)  = R_win_start_idx + R_descend_index(1:4);
            peak_pos_per_win(win_idx*5+4,:)  = L2_win_start_idx + L2_descend_index(1:4);
            peak_pos_per_win(win_idx*5+5,:)  = R2_win_start_idx + R2_descend_index(1:4);
            
            %% center window peak value
            if peak_pos == 1
                peak_value_tmp = fi(peak_value_fix + win_samples_fix(peak_pos+1), SW16F14, F_floor);
            elseif peak_pos == win_len
                peak_value_tmp = fi(peak_value_fix + win_samples_fix(peak_pos-1), SW16F14, F_floor);
            else
                peak_value_tmp = fi(peak_value_fix + win_samples_fix(peak_pos-1) + win_samples_fix(peak_pos+1), SW16F14, F_floor);
            end
            
            %% left1 window peak value
            if L_peak_pos == 1
                L_peak_value_tmp = fi(L_peak_value_fix + L_win_samples_fix(L_peak_pos+1), SW16F14, F_floor);
            elseif L_peak_pos == win_len
                L_peak_value_tmp = fi(L_peak_value_fix + L_win_samples_fix(L_peak_pos-1), SW16F14, F_floor);
            else
                L_peak_value_tmp = fi(L_peak_value_fix + L_win_samples_fix(L_peak_pos-1) + L_win_samples_fix(L_peak_pos+1), SW16F14, F_floor);
            end
            
            %% right1 window peak value
            if R_peak_pos == 1
                R_peak_value_tmp = fi(R_peak_value_fix + R_win_samples_fix(R_peak_pos+1), SW16F14, F_floor);
            elseif R_peak_pos == win_len
                R_peak_value_tmp = fi(R_peak_value_fix + R_win_samples_fix(R_peak_pos-1), SW16F14, F_floor);
            else
                R_peak_value_tmp = fi(R_peak_value_fix + R_win_samples_fix(R_peak_pos-1) + R_win_samples_fix(R_peak_pos+1), SW16F14, F_floor);
            end
            
            %% left2 window peak value
            if L2_peak_pos == 1
                L2_peak_value_tmp = fi(L2_peak_value_fix + L2_win_samples_fix(L2_peak_pos+1), SW16F14, F_floor);
            elseif L2_peak_pos == win_len
                L2_peak_value_tmp = fi(L2_peak_value_fix + L2_win_samples_fix(L2_peak_pos-1), SW16F14, F_floor);
            else
                L2_peak_value_tmp = fi(L2_peak_value_fix + L2_win_samples_fix(L2_peak_pos-1) + L2_win_samples_fix(L2_peak_pos+1), SW16F14, F_floor);
            end
            
            %% right2 window peak value
            if R2_peak_pos == 1
                R2_peak_value_tmp = fi(R2_peak_value_fix + R2_win_samples_fix(R2_peak_pos+1), SW16F14, F_floor);
            elseif R2_peak_pos == win_len
                R2_peak_value_tmp = fi(R2_peak_value_fix + R2_win_samples_fix(R2_peak_pos-1), SW16F14, F_floor);
            else
                R2_peak_value_tmp = fi(R2_peak_value_fix + R2_win_samples_fix(R2_peak_pos-1) + R2_win_samples_fix(R2_peak_pos+1), SW16F14, F_floor);
            end
            
            %% get max peak_value of center window\left window\right window
            peak_tmp = [peak_value_tmp, L_peak_value_tmp, R_peak_value_tmp, L2_peak_value_tmp, R2_peak_value_tmp];
            max_peak_value = max(peak_tmp);
            
            %% get final peak value and peak pos
            if max_peak_value == peak_value_tmp
                peak_value_final_fix  = peak_value_tmp;
                peak_pos_final        = peak_pos;
                peak_org_fix          = peak_value_fix;
            elseif max_peak_value == L_peak_value_tmp
                peak_value_final_fix  = L_peak_value_tmp;
                peak_pos_final        = L_peak_pos;
                peak_org_fix          = L_peak_value_fix;
            elseif max_peak_value == R_peak_value_tmp
                peak_value_final_fix  = R_peak_value_tmp;
                peak_pos_final        = R_peak_pos;
                peak_org_fix          = R_peak_value_fix;
            elseif max_peak_value == L2_peak_value_tmp
                peak_value_final_fix  = L2_peak_value_tmp;
                peak_pos_final        = L2_peak_pos;
                peak_org_fix          = L2_peak_value_fix;
            elseif max_peak_value == R2_peak_value_tmp
                peak_value_final_fix  = R2_peak_value_tmp;
                peak_pos_final        = R2_peak_pos;
                peak_org_fix          = R2_peak_value_fix;
            end
            
            peak_pos_final_all(win_idx+1, :)           = peak_pos_final;
            peak_value_final_per_win_fix(win_idx+1, :) = peak_value_final_fix;
            peak_org_value_per_win_fix(win_idx+1, :)   = fi(peak_org_fix, SW16F14, F_floor);
            
            %% save direction of TA adjustment
            if (peak_pos_final == 0+1)
                P_right_fix   = win_samples_fix(peak_pos_final+1);
                P_left_fix    = fi(0, SW32F30, F_floor);
            elseif (peak_pos_final == (PRACH_Ncs - 1 + 1))
                P_right_fix   = fi(0, SW32F30, F_floor);
                P_left_fix    = win_samples_fix(peak_pos_final-1);
            else
                P_right_fix   = win_samples_fix(peak_pos_final+1);
                P_left_fix    = win_samples_fix(peak_pos_final-1);
            end
            
            if P_right_fix>=P_left_fix
                P_fix = P_right_fix;
                alpha_sign = 1;
            else
                P_fix = P_left_fix;
                alpha_sign = -1;
            end
            
            alpha_sign_per_win(win_idx+1, :)                 = alpha_sign;
            p_for_TA_adjust_enable_per_win(win_idx+1, :)     = fi(P_fix, SW16F14, F_floor);
            
        end %window loop
        
        %% noise value
        peak_pos_idx             = reshape(peak_pos_per_win, 1, []);
        noise_idx                = setdiff((1:1:size(PDP_matrix_fix,2)), peak_pos_idx);
        noise_sample_fix         = fi(PDP_matrix_fix(basic_seq_idx+1, noise_idx), SW16F15, F_floor);
        noise_sample_num         = length(noise_sample_fix);
        noise_sample_shift_fix   = fi(noise_sample_fix, SW16F15, F_floor);
        noise_sample_sum_fix     = fi(sum(noise_sample_shift_fix), SW32F15, F_floor);
        inv_noise_len_fix        = inv_noise_len_list_fix(noise_sample_num);
        noise_avg_fix            = fi(noise_sample_sum_fix*inv_noise_len_fix, SW16F15, F_floor);
        int_noise_avg_fix        = fn_fi2double(noise_avg_fix);
        if int_noise_avg_fix == 0
            noise_avg_fix = fi(5/(2^30), SW16F15, F_floor);
        end
        
        ref_fix = fi(noise_avg_fix*detecting_threshold_fix, SW16F14, F_floor); 
        
        for win_idx = 0:1:(win_num-1)
            peak_value_final_fix = peak_value_final_per_win_fix(win_idx+1, :);
            peak_value_fix       = peak_org_value_per_win_fix(win_idx+1, :);
            alpha_sign           = alpha_sign_per_win(win_idx+1, :);
            peak_pos             = peak_pos_final_all(win_idx+1, :) - 1;
            P_fix                = p_for_TA_adjust_enable_per_win(win_idx+1, :);
            preamble_ID          = basic_seq_idx*win_num + win_idx;
            
            switch detecting_mode
                case 1
                    peak_value_current_mode_fix = peak_value_final_fix;
                case 2
                    peak_value_current_mode_fix = peak_value_fix;
            end
            
            if peak_value_final_fix > ref_fix
                RA_status_matrix(preamble_ID+1) = 1;
                %% measure preamble power
                
                %% TA
                ref_alpha_fix = fi(P_fix*V_threshold_fix, SW16F14, F_floor);
                if peak_value_fix<ref_alpha_fix
                    TA_current = fi(peak_pos, TA_result_fix.numerictype, F_floor) + alpha_sign*alpha_fix;
                else
                    TA_current = fi(peak_pos, TA_result_fix.numerictype, F_floor);
                end
                TA_current_fix = fi(TA_current, TA_result_fix.numerictype, F_floor);
                
                TA_result_fix(preamble_ID+1) = TA_current_fix;
            else
                RA_status_matrix(basic_seq_idx*win_num + win_idx + 1) = 0;
            end
        end % window loop
        
    end %basic sequence loop
    
end
