function soft_bits   = demodulation_3GPP(modulated_symbols, modulation_mode)

symbol_num = length(modulated_symbols);

C_re = real(modulated_symbols);
C_im = imag(modulated_symbols);

switch (modulation_mode)
    %% BPSk
    case 1

    %% QPSK
    case 2
     D                  = 1/sqrt(2);
     demod_bits         = zeros(2,symbol_num);
     demod_bits(1,:)    = -2*D*C_re;
     demod_bits(2,:)    = -2*D*C_im;
     soft_bits          = reshape(demod_bits, 1, []);
end