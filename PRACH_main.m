%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    PRACH_main
%
% Function:     Main function
%               This link is used for simulating PRACH
%
% Input:        None
% Output:       None
%
% Modification History
% --------------------
% Date      20180125
% Author    ZONG Yaozong
% Content   Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
clear all;
close all;
clc;

%% load lib files
fn_load_lib_C_model;

%% global variable
fn_global_variable_table;
fn_global_variable_definition;
fn_fix_point_format_table;
fn_fix_point_format_definition;

%% simulation parameter configuration
fn_sim_para_cfg;
g_testvector_enable       = 0;    % save testvector 0-do not save 1-save 
g_progress_disp_enable    = 1;    % display simluation progress 0-do not 1-display

if g_testvector_enable == 1
    file_name = strcat(g_testvector_path,'\*');
    delete(file_name);
end

%% cell parameter configuration
fn_cell_para_cfg;

%% UE parameter configuration
fn_UE_para_cfg;

%% display simulation information
display_sim_info;

rand('state', 2^13-1);
randn('state', 2^17-1);

start_CPU_clock = tic;

for sim_snr_idx = 0:1:(g_sim_para.snr_num-1)
    
    sfn_current             = g_sim_para.sfn_start_idx;
    sim_frame_num_total     = g_sim_para.sim_frame_num_list(sim_snr_idx+1);   
    
    for sim_frame_idx = 0:1:(sim_frame_num_total-1)
        
        UE_num              = g_sim_para.UE_num;
                
        for sim_slot_idx = g_sim_para.slot_start_idx:1:g_sim_para.slot_stop_idx
            
            if (sim_frame_idx == 0) && (sim_slot_idx == g_sim_para.slot_start_idx)
                transmitting_signal_pre_slot = zeros(UE_num, max(g_sim_para.UE_ant_num_list), g_cell_para.PRACH_para.PRACH_total_len);
            end
            
            signal_passed_channel = [];
            
            for sim_UE_idx = 0:1:(UE_num-1)
                %% read UE information
                UE_info_current             = g_UE_para(sim_UE_idx+1);
                transmission_delay_current  = UE_info_current.transmission_delay;
                doppler_freq_offset         = UE_info_current.doppler_freq_offset;
                signal_sample_rate          = g_cell_para.baseband_sample_rate;
                
                %% transmitting module
                transmitting_signal_current_slot = fn_transmitting_module(UE_info_current);
                                
                if(transmission_delay_current~=0)
                    if (transmission_delay_current > 0)
                        signal_part_a = transmitting_signal_pre_slot(sim_UE_idx+1, :, end-transmission_delay_current+1:end);
                        signal_part_a = squeeze(signal_part_a);
                        if size(signal_part_a, 1) ~= size(transmitting_signal_pre_slot, 2)
                            signal_part_a = signal_part_a.';
                        end
                        signal_part_b = transmitting_signal_current_slot(:, 1:end-transmission_delay_current); 
                        channle_input = [signal_part_a, signal_part_b];
                    else
                        error('delay can not be negative number in current version');
                    end
                else
                    channle_input = transmitting_signal_current_slot;
                end
                
                transmitting_signal_pre_slot(sim_UE_idx+1, :, :) = transmitting_signal_current_slot;
                                
                %% channel module
                signal_passed_channel_current_UE    = fn_channel_module(channle_input, signal_sample_rate, doppler_freq_offset);
                if isempty(signal_passed_channel)
                    signal_passed_channel           = signal_passed_channel_current_UE;
                else
                    signal_passed_channel           = signal_passed_channel + signal_passed_channel_current_UE;
                end
                
            end % UE loop
                                                      
            %% receiving module
            fn_receiveing_module(signal_passed_channel);
        end % slot loop
         
    end % frame loop
    
end % snr loop

disp('Simulation is Completed');
toc(start_CPU_clock)
