%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_transmitting_module
%
% Function:     transmitting module
%
% Input:        None
% Output:       transmitting_signal - transmitting signal            
%
% Modification History
% Date      20180125
% Author    ZONG Yaozong, WANG Jinguo
% Content   Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function transmitting_signal = fn_transmitting_module(UE_info)
fn_global_variable_table;

%% read parameter
PRACH_para                          = g_cell_para.PRACH_para;
preamble_SCS                        = g_cell_para.PRACH_para.SCS;
preamble_resource_pool_freq_domain  = PRACH_para.preamble_pool_freq_domain;
preamble_idx                        = UE_info.preamble_idx;
seq_repetion_num                    = PRACH_para.sequence_rep_num;
PRACH_CP_len                        = PRACH_para.CP_len;
PRACH_GP_len                        = PRACH_para.GP_len;
UE_ant_num                          = UE_info.UE_ant_num;

%% initialize
transmitting_signal             = [];
transmitting_signal_without_CP  = [];

%% generate PRACH signal
%% read frequency preamble sequence
preamble_freq_domain    = preamble_resource_pool_freq_domain(preamble_idx+1,:);

%% frequency mapping
freq_mapping_pattern                         = fn_freq_mapping_pattern_generator(g_cell_para, UE_info);
freq_symbol                                  = zeros(1, length(freq_mapping_pattern)); 
freq_symbol(find(freq_mapping_pattern == 3)) = preamble_freq_domain;
g_tmp.g_tmp_freq_mapping_pattern             = freq_mapping_pattern; % ��ʱ���� ��ɾ��

%% IFFT
transmitting_signal_current_symbol = fn_OFDM_modulator(freq_symbol, 0);

%% repetion
for seq_rep_idx = 1:1:seq_repetion_num
    transmitting_signal_without_CP = [transmitting_signal_without_CP, transmitting_signal_current_symbol];
end

%% add CP and GP
transmitting_signal = [transmitting_signal_without_CP((end-PRACH_CP_len+1):end), transmitting_signal_without_CP, zeros(1,PRACH_GP_len)];

%% half SCS freq offset
if g_sim_para.half_SCS_freq_offset_enable == 1
    transmitting_signal = transmitting_signal.*exp(1j*2*pi*((g_cell_para.SCS)/2)*[0:1:(length(transmitting_signal)-1)]*(1/g_cell_para.baseband_sample_rate));
end

transmitting_signal = repmat(transmitting_signal,UE_ant_num,1);

end