%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_PDP_detector_fix
%
% Function:     access detector
%               
%
% Input:        PDP_matrix_fix                         - PDP
%               PRACH_restricted_set_cfg               - restricted set configration, 0-unrestricted 1-restricted type A 2-restricted type B, higher-layer parameter
%               PRACH_Ncs                              - PRACH Ncs
%               PRACH_Cv                               - Cv, please refer to 3PGG TS 38.211
%               PRACH_du                               - du, please refer to 3PGG TS 38.211
%
% Output:       TA_matrix_fix                          - TA
%
% Modification History
% --------------------
% Date      20180125
% Author    ZONG Yaozong
% Content   Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function [RA_status_matrix, preamble_power_matrix_fix, TA_result_fix] = fn_PDP_detector_unrestricted_fix(PDP_matrix_fix, PRACH_restricted_set_cfg, PRACH_Ncs, PRACH_Cv, PRACH_du, detecting_threshold_fix, plot_PDP_enable)
fn_fix_point_format_table;

[basic_seq_num, PDP_len]    = size(PDP_matrix_fix);
Cv_len                      = length(PRACH_Cv);
win_idx_list                = 0:1:(Cv_len-1);



inv_noise_len_list_fix      = fi(1./[1:839], SW16F12, F_floor);

RA_status_matrix            = zeros(1,64);
preamble_power_matrix_fix   = fi(zeros(1,64), SW32F28, F_floor);
TA_result_fix               = fi(-1*ones(1,64), SW32F22, F_floor);

V_threshold_fix             = fi(2, numerictype(1, 8, 0), F_floor);
alpha_fix                   = fi(0.5, TA_result_fix.numerictype, F_floor);

if PRACH_restricted_set_cfg == 0
    % win_start_idx_list   = mod(PDP_len - win_idx_list*PRACH_Ncs, PDP_len);
   win_start_idx_list   = mod(PDP_len - PRACH_Cv, PDP_len);
   win_stop_idx_list    = mod(PDP_len - (win_idx_list-1)*PRACH_Ncs-1, PDP_len);
   win_num              = length(win_start_idx_list);
      
   for basic_seq_idx = 0:1:(basic_seq_num-1)
       if plot_PDP_enable == 1
           figure
           plot_int_PDP_current_seq = fn_fi2double(PDP_matrix_fix(basic_seq_idx+1, :));
           plot_PDP_current_seq_len = length(plot_int_PDP_current_seq);
           x_list = 0:1:(plot_PDP_current_seq_len-1);
           plot(x_list, plot_int_PDP_current_seq);
           hold on;
           grid on;
           plot_win_boundary = 0*ones(1,plot_PDP_current_seq_len);
           for win_idx = 0:1:(win_num-1) 
               win_start_idx            = win_start_idx_list(win_idx+1);
               win_stop_idx             = win_stop_idx_list(win_idx+1);
               plot_win_boundary((win_start_idx+1):(win_stop_idx+1)) = -max(plot_int_PDP_current_seq)*0.15;
               plot_win_boundary((win_start_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
               plot_win_boundary((win_stop_idx+1)) = max(plot_int_PDP_current_seq)*0.75;
           end
           plot(x_list,plot_win_boundary, '--r');
           title(strcat('PDP of sequence ', num2str(basic_seq_idx)));
       end
       
       peak_pos_per_win                     = zeros(win_num, 1);                                  % save peak pos
       peak_value_per_win_fix               = fi(zeros(win_num, 1), SW16F15, F_floor);
       peak_value_final_per_win_fix         = fi(zeros(win_num, 1), SW32F30, F_floor);            % save combining peak value
       peak_group_index_per_win             = zeros(win_num, 4);                                  % save peak group index
       alpha_sign_per_win                   = zeros(win_num, 1);                                  % save direction of TA adjustment
       p_2ed_max_per_win                    = fi(zeros(win_num, 1), SW32F30, F_floor);            
       
       for win_idx = 0:1:(win_num-1)
           %% read samples of current window
           win_start_idx            = win_start_idx_list(win_idx+1);
           win_stop_idx             = win_stop_idx_list(win_idx+1);
           win_samples_fix          = fi(PDP_matrix_fix(basic_seq_idx+1, (win_start_idx+1):(win_stop_idx+1)), SW32F30, F_floor);
           win_len                  = length(win_samples_fix);
           
           %% calculate combining peak value and noise value
           %% sort
           [tmp, descend_index]                     = sort(double(win_samples_fix), 2, 'descend');
           peak_pos                                 = descend_index(1);
           peak_value_fix                           = win_samples_fix(peak_pos);
           peak_pos_per_win(win_idx+1, :)           = peak_pos;
           peak_group_index_per_win(win_idx+1, :)   = descend_index(1:4) + win_start_idx;
           peak_value_per_win_fix(win_idx+1, :)     = peak_value_fix;
           
           %% peak value
           if peak_pos == 1
               peak_value_final_fix = fi(peak_value_fix + win_samples_fix(peak_pos+1), SW32F30, F_floor);
           elseif peak_pos == win_len
               peak_value_final_fix = fi(peak_value_fix + win_samples_fix(peak_pos-1), SW32F30, F_floor);
           else
               peak_value_final_fix = fi(peak_value_fix + win_samples_fix(peak_pos-1) + win_samples_fix(peak_pos+1), SW32F30, F_floor);
           end
           
           peak_value_final_per_win_fix(win_idx+1, :) = peak_value_final_fix;
                      
           %% save direction of TA adjustment
           if (peak_pos == 0+1)
               P_right_fix   = win_samples_fix(peak_pos+1);
               P_left_fix    = fi(0, SW32F30, F_floor);
           elseif (peak_pos == (PRACH_Ncs - 1 + 1))
               P_right_fix   = fi(0, SW32F30, F_floor);
               P_left_fix    = win_samples_fix(peak_pos-1);
           else
               P_right_fix   = win_samples_fix(peak_pos+1);
               P_left_fix    = win_samples_fix(peak_pos-1);
           end
           
           if P_right_fix>=P_left_fix
               P_2ed_max_current_fix = P_right_fix;
               alpha_sign = 1;
           else
               P_2ed_max_current_fix = P_left_fix;
               alpha_sign = -1;
           end
               
           alpha_sign_per_win(win_idx+1, :)                 = alpha_sign;
           p_2ed_max_per_win(win_idx+1, :)                  = P_2ed_max_current_fix;
       end % window loop
       
       %% noise value
       peak_group_index_all_win             = reshape(peak_group_index_per_win.', 1, []);
       noise_index_current_PDP              = setdiff([1:1:size(PDP_matrix_fix,2)], peak_group_index_all_win);
       nosie_samples_current_PDP_fix        = fi(PDP_matrix_fix(basic_seq_idx+1, noise_index_current_PDP), SW32F30, F_floor);
       noise_sample_num                     = length(nosie_samples_current_PDP_fix);
       noise_sample_shift_fix               = fi(nosie_samples_current_PDP_fix, SW32F16, F_floor);
       noise_sample_sum_fix                 = fi(sum(noise_sample_shift_fix), SW32F16, F_floor);
       inv_noise_len_fix                    = inv_noise_len_list_fix(noise_sample_num);
       noise_avg_tmp_fix                    = fi(noise_sample_sum_fix*inv_noise_len_fix, SW32F30, F_floor);
       noise_avg_fix                        = fi(noise_avg_tmp_fix, SW32F30, F_floor);
       int_noise_avg_fix                    = fn_fi2double(noise_avg_fix);
       if int_noise_avg_fix == 0
           noise_avg_fix = fi(5/(2^15), noise_avg_fix.numerictype, F_floor);
       end
       
       ref_fix = fi(noise_avg_fix*detecting_threshold_fix, SW32F30, F_floor); 
       
       for win_idx = 0:1:(win_num-1)
           peak_value_fix           = peak_value_per_win_fix(win_idx+1, :);
           peak_value_final_fix     = peak_value_final_per_win_fix(win_idx+1, :);
           alpha_sign               = alpha_sign_per_win(win_idx+1, :);
           peak_pos                 = peak_pos_per_win(win_idx+1, :) - 1;
           P_2ed_max_current_fix    = p_2ed_max_per_win(win_idx+1, :);
           
           preamble_ID = basic_seq_idx*win_num + win_idx;
           if peak_value_final_fix > ref_fix
               RA_status_matrix(preamble_ID+1) = 1;
               %% measure preamble power
               
               %% TA              
               ref_alpha_fix = fi(P_2ed_max_current_fix*V_threshold_fix, SW32F30, F_floor);
               if real(peak_value_fix)<real(ref_alpha_fix)
                   TA_current = fi(peak_pos, TA_result_fix.numerictype, F_floor) + alpha_sign*alpha_fix;
               else
                   TA_current = fi(peak_pos, TA_result_fix.numerictype, F_floor);
               end
               TA_current_fix = fi(TA_current, TA_result_fix.numerictype, F_floor);
               
               TA_result_fix(preamble_ID+1) = TA_current_fix;
           else
               RA_status_matrix(basic_seq_idx*win_num + win_idx + 1) = 0;
           end
           
       end % window loop
       
   end % basic sequence loop

end


end