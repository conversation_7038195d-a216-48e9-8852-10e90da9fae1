% Function:     generate testvector
% Input:        input_original  - input datas
%               out_file_name   - file name of testvector
%               out_file_path   - file path
%               complement_flag - convert input to complement 0-unable 1-enable
%               QI_comb_mode    - format of IQ in the testvector
% History
% Author          Date          Content
% ZONG Yaozong    20170519    create file
function fn_testvector_generator(input_original, out_file_name, out_file_path, complement_flag, QI_comb_mode, word_len)

if isfi(input_original)
    fractionLength = input_original.FractionLength;
    input_real_fix = real(input_original);
    % int_real = double(int(input_real_fix));
    int_real = floor(double(input_real_fix)*(2^fractionLength));
    
    input_imag_fix = imag(input_original);
    % int_imag = double(int(input_imag_fix));
    int_imag = floor(double(input_imag_fix)*(2^fractionLength));
    
    word_len = input_real_fix.WordLength;
else
    int_real = real(input_original);
    int_imag = imag(input_original);
end


if complement_flag == 1
    %% complement
    int_real(int_real<0) = (2^word_len) - abs(int_real(int_real<0));
    int_imag(int_imag<0) = (2^word_len) - abs(int_imag(int_imag<0));
end

element_len = num2str(ceil(word_len/4));
element_format = strcat('%', '0', element_len, 'X');

switch QI_comb_mode
    %% format of IQ
    case 1
        % in one file [I Q]
        element_format = [element_format,' ',element_format,'\n'];
        file_name = fullfile(out_file_path, strcat(out_file_name,'.dat'));
        fid = fopen(file_name, 'w');
        fprintf(fid, element_format, [int_real;int_imag]);
        fclose(fid);
        
    case 2
        % in one file [IQ]
        data_tmp = int_real*(2^word_len) + int_imag;
        % element_format = strcat('0x%', '0', num2str(2*ceil(word_len/4)), 'X', ',\n');
        element_format = strcat('%', '0', num2str(2*ceil(word_len/4)), 'X', '\n');
        file_name = fullfile(out_file_path, strcat(out_file_name,'.dat'));
        fid = fopen(file_name, 'w');
        fprintf(fid, element_format, data_tmp);
        fclose(fid);
        
        
    case 3
        % in two files
        element_format = [element_format,'\n'];
        file_name = fullfile(out_file_path, strcat(out_file_name,'_real','.dat'));
        fid = fopen(file_name, 'w');
        fprintf(fid, element_format, int_real);
        fclose(fid);
        
        file_name = fullfile(out_file_path, strcat(out_file_name,'_imag','.dat'));
        fid = fopen(file_name, 'w');
        fprintf(fid, element_format, int_imag);
        fclose(fid);
        
    case 4
        % only real part
        element_format = [element_format,'\n'];
        file_name = fullfile(out_file_path, strcat(out_file_name,'.dat'));
        fid = fopen(file_name, 'w');
        fprintf(fid, element_format, int_real);
        fclose(fid);

end

end

