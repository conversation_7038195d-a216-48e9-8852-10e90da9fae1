
if ispc
    addpath(strcat(pwd, '/C_model/xilinx_C_model_FFT_64bits') );
    addpath(strcat(pwd, '/C_model/xilinx_C_model_FFT_32bits') );
    addpath(strcat(pwd, '/C_model/DSP_C_model') );
    addpath(strcat(pwd, '/function_lib') );
else
    addpath(strcat(pwd, '\C_model\xilinx_C_model_FFT_64bits') );
    addpath(strcat(pwd, '\C_model\xilinx_C_model_FFT_32bits') );
    addpath(strcat(pwd, '\C_model\DSP_C_model') );
    addpath(strcat(pwd, '\function_lib') );
end
