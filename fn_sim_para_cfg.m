%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_sim_para_cfg
%
% Function:     Configure frequently changing parameter
%
% Input:        None
% Output:       None
%
% Modification History
% Date      20180125
% Author    ZONG Yaozong
% Content   Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function fn_sim_para_cfg
fn_global_variable_table;

%% test mode switch
g_sim_para.test_mode_enable         = g_SWITCHON;                   % test mode switch g_SWITCHON/g_SWITCHOFF 

%% UE number
g_sim_para.UE_num                   = 1;                            % UE number in current simulation 

%% SNR ,simulation iteration, delay 
g_sim_para.snr_list = [39:40;
                       39:40];                                      % SNR list for each UE, dimension:UE*SNR
g_sim_para.sim_frame_num_list = [001];                              % frame number for each SNR
g_sim_para.snr_num                  = length(g_sim_para.snr_list);  % SNR number
g_sim_para.sfn_start_idx            = 0;                            % first sfn in simulation
g_sim_para.slot_start_idx           = 0;                            % first slot index in a simulation frame
g_sim_para.slot_stop_idx            = 0;                            % last slot index in a simulation frame

g_sim_para.transmission_delay_list  = [ceil(1*4096*30000/(839*1250));
                                       00];                         % delay for each UE, unit:sample in baseband, postive:preamble is late, dimension:UE*delay, 
                                                                    % e.g. system bandwith=100MHz,u=1,PRACH format0, unit =(1/(4096*30000))second
g_sim_para.half_SCS_freq_offset_enable = 0;                         % half subcarrier space frequence offset in transmitter 0-switch off 1-switch on
%% frame structure and PRACH configuration
g_sim_para.N_start_BWP              = [000];                        % the lowest numbered resource block of the initial active uplink bandwidth part based on common resource block indexing, dimension:BWP*N_start_BWP
g_sim_para.N_BWP_size               = [100]; 
g_sim_para.n_start_RA               = [000];                        % the frequency offset of lowest PRACH transmission occasion in frequency domain with respect to PRB 0 of the initial active uplink bandwidth part
g_sim_para.lowest_numbered_sc_k_u_0 = 0;                            % [-6 0 6] obtained from the higher-layer parameter k0 and is such that the lowest numbered subcarrier in a common resource block for subcarrier spacing configuration u coincides with the lowest numbered subcarrier in a common resource block for any subcarrier spacing configuration less than u

g_sim_para.frame_structure_u        = 1;                            % u for frame structure
g_sim_para.PRACH_format             = '0';                          % PRACH format
g_sim_para.PRACHRootSqeuenceIndex   = 024;                          % logic root index of PRACH sequence, higher-layer parameter
g_sim_para.restrictedSetConfig      = 0;                            % 0-unrestricted 1-restricted type A 2-restricted type B, higher-layer parameter
g_sim_para.PRACH_zeroCorrelationZoneConfig = 01;                    % zeros correlation zone configration, higher-layer parameter
g_sim_para.PRACH_freq_division_num  = [1];                          % PRACH frequence division number
g_sim_para.PRACH_freq_division_idx_list = [0;1];                    % PRACH index in frequence division, dimension:UE*index [0,2]
if g_sim_para.PRACH_freq_division_num == 1
   g_sim_para.PRACH_freq_division_idx_list = zeros(size(g_sim_para.PRACH_freq_division_idx_list,1),1);
end

g_sim_para.preamble_ID_list         = [00, 05];                     % preamble ID for each UE;
g_sim_para.sim_bandwidth            = 100*(10^6);                   % system bandwith

%% channel
g_sim_para.no_noise_enable          = 1;                            % noise switch 0-transmiting signal will go through channel with noise 1-transmiting signal will not go through channel without noise
g_sim_para.doppler_freq_offset      = [000;000];                    % doppler frequency shift,unit:Hz, dimension:UE*freq_offset

%% antenna
g_sim_para.UE_ant_num_list          = 2;                            % transmitting antennas number
g_sim_para.gNB_num                  = 16;                           % receiving antennas number

%%
g_sim_para.snr_idx                  = 0;                            % initialize SNR index
g_sim_para.channel_rand_phase       = [];                           % record random phase in channel

g_sim_para.test_mode_enable         = 0;                            % 0-use simulation data 1-
g_sim_para.plot_PDP_enable          = 1;                            % 0-Do not plot PDP 1-plot PDP 

warning('on', 'fixed:fi:overflow');
warning('off','fixed:fi:underflow');
warning on backtrace
fipref('LoggingMode','on');

end