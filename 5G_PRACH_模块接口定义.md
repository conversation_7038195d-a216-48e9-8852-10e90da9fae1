# 5G PRACH接收模块接口定义文档

## 1. 总体模块架构

```
输入信号 → [预处理模块] → [AGC模块] → [频域搬移模块] → [降采样模块] → 
[OFDM解调模块] → [序列提取模块] → [相关计算模块] → [PDP组合模块] → [检测判决模块] → 输出结果
```

## 2. 各模块详细接口定义

### 2.1 预处理模块 (Preprocessing Module)

**模块名称：** `A_preprocessing`

**功能：** 半子载波频偏补偿和循环前缀移除

**输入接口：**
- `A_in`: 接收信号
  - 类型：复数数组
  - 格式：`complex<float>`
  - 维度：`[N_rx × N_samples]`
  - 范围：[-1, +1]

- `A_cfg`: 配置参数
  - `SCS`: 子载波间隔 (Hz)
  - `Fs`: 采样率 (Hz) 
  - `CP_len`: 循环前缀长度
  - `freq_offset_en`: 频偏补偿使能标志

**输出接口：**
- `A_out`: 预处理后信号
  - 类型：复数数组
  - 格式：`complex<float>`
  - 维度：`[N_rx × (N_rep × N_u)]`
  - 范围：[-1, +1]

**处理延迟：** 1 clock cycle

---

### 2.2 AGC模块 (Automatic Gain Control)

**模块名称：** `B_agc`

**功能：** 时域自动增益控制

**输入接口：**
- `B_in`: 预处理信号
  - 类型：复数数组
  - 格式：`complex<float>`
  - 维度：`[N_rx × N_u]` (单个重复)
  - 范围：[-1, +1]

- `B_cfg`: AGC配置
  - `target_bits`: 目标位数 (默认13)
  - `ref_length`: 参考长度 (默认2048)
  - `agc_enable`: AGC使能标志

**输出接口：**
- `B_out`: AGC调整后信号
  - 类型：复数数组
  - 格式：`complex<SW16F15>`
  - 维度：`[N_rx × N_u]`
  - 范围：[-1, +1-2^(-15)]

- `B_gain`: AGC增益因子
  - 类型：整数
  - 格式：`int8`
  - 维度：`[N_rx × 1]`
  - 范围：[-8, +8]

**处理延迟：** 2048 clock cycles (参考信号统计)

---

### 2.3 频域搬移模块 (Frequency Shift)

**模块名称：** `C_freq_shift`

**功能：** 将PRACH信号搬移到零频

**输入接口：**
- `C_in`: AGC调整信号
  - 类型：复数数组
  - 格式：`complex<SW16F15>`
  - 维度：`[N_rx × N_u]`

- `C_cfg`: 频域配置
  - `k_BWP`: BWP起始子载波
  - `k_DC`: DC子载波索引
  - `L_RA`: PRACH序列长度
  - `SCS_PRACH`: PRACH子载波间隔

**输出接口：**
- `C_out`: 频域搬移后信号
  - 类型：复数数组
  - 格式：`complex<SW16F15>`
  - 维度：`[N_rx × N_u]`

- `C_shift_info`: 搬移信息
  - `shift_sc_num`: 搬移子载波数
  - `freq_shift`: 频移量 (Hz)

**处理延迟：** N_u clock cycles (流水线处理)

---

### 2.4 降采样模块 (Downsampling)

**模块名称：** `D_downsample`

**功能：** 多级滤波降采样

**输入接口：**
- `D_in`: 频域搬移信号
  - 类型：复数数组
  - 格式：`complex<SW16F15>`
  - 维度：`[N_rx × N_u]`

- `D_cfg`: 降采样配置
  - `L_RA`: PRACH序列长度 {139, 839}
  - `downsample_ratio`: 降采样比例
  - `filter_mode`: 滤波器模式

**输出接口：**
- `D_out`: 降采样后信号
  - 类型：复数数组
  - 格式：`complex<SW16F15>`
  - 维度：`[N_rx × L_out]`
  - 其中：L_out = 1024 (L_RA=839) 或 256 (L_RA=139)

**处理延迟：** 
- CIC滤波: 12 clock cycles
- FIR滤波: filter_length clock cycles

---

### 2.5 OFDM解调模块 (OFDM Demodulation)

**模块名称：** `E_ofdm_demod`

**功能：** FFT变换和频域处理

**输入接口：**
- `E_in`: 降采样信号
  - 类型：复数数组
  - 格式：`complex<SW16F15>`
  - 维度：`[N_rx × L_out]`

- `E_cfg`: FFT配置
  - `fft_size`: FFT长度
  - `fft_mode`: FFT模式 (Xilinx/MATLAB)

**输出接口：**
- `E_out`: 频域信号
  - 类型：复数数组
  - 格式：`complex<SW16F15>`
  - 维度：`[N_rx × L_out]`

- `E_exp`: FFT指数
  - 类型：整数
  - 格式：`int8`
  - 维度：`[N_rx × 1]`
  - 范围：[-8, +8]

**处理延迟：** log₂(L_out) clock cycles

---

### 2.6 序列提取模块 (Sequence Extraction)

**模块名称：** `F_extract`

**功能：** 从频域信号中提取PRACH序列

**输入接口：**
- `F_in`: 频域信号
  - 类型：复数数组
  - 格式：`complex<SW16F15>`
  - 维度：`[N_rx × L_out]`

- `F_cfg`: 提取配置
  - `L_RA`: PRACH序列长度

**输出接口：**
- `F_out`: 提取的PRACH序列
  - 类型：复数数组
  - 格式：`complex<SW16F15>`
  - 维度：`[N_rx × L_RA]`

**处理延迟：** 1 clock cycle

---

### 2.7 相关计算模块 (Correlation)

**模块名称：** `G_correlation`

**功能：** 频域相关和IFFT处理

**输入接口：**
- `G_in`: PRACH序列
  - 类型：复数数组
  - 格式：`complex<SW16F15>`
  - 维度：`[N_rx × L_RA]`

- `G_local`: 本地序列
  - 类型：复数数组
  - 格式：`complex<SW16F15>`
  - 维度：`[N_root × L_RA]`

- `G_cfg`: 相关配置
  - `N_root`: 基本序列数量
  - `ifft_mode`: IFFT模式

**输出接口：**
- `G_out`: 时域相关结果
  - 类型：复数数组
  - 格式：`complex<SW16F15>`
  - 维度：`[N_rep × N_root × N_rx × L_RA]`

- `G_exp`: IFFT指数
  - 类型：整数
  - 格式：`int8`
  - 维度：`[N_rep × N_root × N_rx]`

**处理延迟：** N_root × log₂(L_RA) clock cycles

---

### 2.8 PDP组合模块 (PDP Combination)

**模块名称：** `H_pdp_combine`

**功能：** 功率延迟分布计算和AGC补偿

**输入接口：**
- `H_in`: 时域相关结果
  - 类型：复数数组
  - 格式：`complex<SW16F15>`
  - 维度：`[N_rep × N_root × N_rx × L_RA]`

- `H_agc`: AGC因子列表
  - 类型：整数数组
  - 格式：`int8`
  - 维度：`[N_rep × N_rx]`

- `H_fft_exp`: FFT指数列表
  - 类型：整数数组
  - 格式：`int8`
  - 维度：`[N_rep × N_rx]`

- `H_ifft_exp`: IFFT指数列表
  - 类型：整数数组
  - 格式：`int8`
  - 维度：`[N_rep × N_root × N_rx]`

**输出接口：**
- `H_out`: PDP矩阵
  - 类型：实数数组
  - 格式：`SW32F30`
  - 维度：`[N_root × L_RA]`
  - 范围：[0, 2^2-2^(-30)]

**处理延迟：** N_rep × N_rx clock cycles

---

### 2.9 检测判决模块 (Detection)

**模块名称：** `I_detection`

**功能：** 前导检测和时间对齐估计

**输入接口：**
- `I_in`: PDP矩阵
  - 类型：实数数组
  - 格式：`SW32F30`
  - 维度：`[N_root × L_RA]`

- `I_cfg`: 检测配置
  - `N_cs`: 循环移位参数
  - `C_v`: 循环移位集合
  - `threshold`: 检测门限
  - `restricted_set`: 限制集配置

**输出接口：**
- `I_status`: 检测状态
  - 类型：二进制数组
  - 格式：`uint8`
  - 维度：`[1 × 64]`
  - 值：{0, 1}

- `I_power`: 前导功率
  - 类型：实数数组
  - 格式：`SW32F28`
  - 维度：`[1 × 64]`

- `I_ta`: 时间对齐
  - 类型：实数数组
  - 格式：`SW32F22`
  - 维度：`[1 × 64]`
  - 范围：[-1, L_RA-1]

**处理延迟：** N_root × N_window clock cycles

## 3. 全局配置参数

### 3.1 系统参数
```
N_rx: 接收天线数 (1-8)
N_rep: 序列重复次数 (1-4)  
N_root: 基本序列数量 (1-64)
L_RA: PRACH序列长度 {139, 839}
```

### 3.2 定点格式
```
SW16F15: 16位有符号，15位小数
SW32F30: 32位有符号，30位小数
SW32F28: 32位有符号，28位小数
SW32F25: 32位有符号，25位小数
SW32F22: 32位有符号，22位小数
SW64F30: 64位有符号，30位小数
```

### 3.3 性能指标
```
总处理延迟: < 1000 clock cycles
吞吐率: 1 PRACH/ms
检测概率: > 99% (SNR > -10dB)
虚警概率: < 1%
TA估计精度: ±0.5 采样点
```

该接口定义为硬件实现和软件仿真提供了完整的模块化设计参考。
