function [output, blkexp] = fn_xilinx_FFT(input_for_C_model, direction, C_NFFT_MAX, C_ARCH, C_HAS_NFFT, C_USE_FLT_PT, C_INPUT_WIDTH, C_TWIDDLE_WIDTH, C_HAS_SCALING, C_HAS_BFP, C_HAS_ROUNDING, check_flag)

% input_for_C_model:    input
% direction:            FFT (1) or IFFT (0)
% C_NFFT_MAX:           FFT/IFFT size (exponent)
% C_ARCH:               3
% C_HAS_NFFT:           0
% C_USE_FLT_PT:         0
% C_INPUT_WIDTH:        16
% C_TWIDDLE_WIDTH:      16
% C_HAS_SCALING:        1
% C_HAS_BFP:            1
% C_HAS_ROUNDING:       1

channels = 1;

generics.C_NFFT_MAX = C_NFFT_MAX;
generics.C_ARCH = C_ARCH;
generics.C_HAS_NFFT = C_HAS_NFFT;
generics.C_USE_FLT_PT = C_USE_FLT_PT;
generics.C_INPUT_WIDTH = C_INPUT_WIDTH; % Must be 32 if C_USE_FLT_PT = 1
generics.C_TWIDDLE_WIDTH = C_TWIDDLE_WIDTH; % Must be 24 or 25 if C_USE_FLT_PT = 1
generics.C_HAS_SCALING = C_HAS_SCALING; % Set to 0 if C_USE_FLT_PT = 1
generics.C_HAS_BFP = C_HAS_BFP; % Set to 0 if C_USE_FLT_PT = 1
generics.C_HAS_ROUNDING = C_HAS_ROUNDING; % Set to 0 if C_USE_FLT_PT = 1

samples = 2^generics.C_NFFT_MAX;

% Handle multichannel FFTs if required
for channel = 1:channels
 
  % Create input data frame
  % input_raw = input_for_C_model;
  input_raw(1:samples) = input_for_C_model(1:samples);
    
  if generics.C_USE_FLT_PT == 0
    % Set up quantizer for correct twos's complement, fixed-point format: one sign bit, C_INPUT_WIDTH-1 fractional bits
    q = quantizer([generics.C_INPUT_WIDTH, generics.C_INPUT_WIDTH-1], 'fixed', 'convergent', 'saturate');
    % Format data for fixed-point input
    input = quantize(q,input_raw);
  else
    % Floating point interface - use data directly
    input = input_raw;
  end
  
  % Set point size for this transform
  nfft = generics.C_NFFT_MAX;
  
  % Set up scaling schedule: scaling_sch[1] is the scaling for the first stage
  % Scaling schedule to 1/N: 
  %    2 in each stage for Radix-4/Pipelined, Streaming I/O
  %    1 in each stage for Radix-2/Radix-2 Lite
  if generics.C_ARCH == 1 || generics.C_ARCH == 3
    scaling_sch = ones(1,floor(nfft/2)) * 2;
    if mod(nfft,2) == 1
      scaling_sch = [scaling_sch 1];
    end
  else
    scaling_sch = ones(1,nfft);
  end
    
  % Run the MEX function
  [output, blkexp, overflow] = xfft_v9_0_bitacc_mex(generics, nfft, input, scaling_sch, direction);
  
  if check_flag == 1
      
      % Check output values are correct
      % The FFT of constant input data is an impulse
      % Therefore all output samples should be zero except for the first
      % The value of the first sample depens on the type of scaling used
      if generics.C_USE_FLT_PT == 0
          if generics.C_HAS_SCALING == 0
              expected_xk_re_0 = input_for_C_model * (2^generics.C_NFFT_MAX);
          else
              expected_xk_re_0 = input_for_C_model;
          end
      else
          expected_xk_re_0 = 512 + 512j;
      end
      
      % Check xk_re and xk_im data: Only xk_re[0] should be non-zero
      if output(1) ~= expected_xk_re_0
          if channels > 1
              error('ERROR: Channel %d xk_re[0] is incorrect: expected %f + j%f, actual %f + j%f\n',channel,real(expected_xk_re_0),imag(expected_xk_re_0),real(output(1)),imag(output(1)))
          else
              error('ERROR: xk_re[0] is incorrect: expected %f + j%f, actual %f + j%f\n',real(expected_xk_re_0),imag(expected_xk_re_0),real(output(1)),imag(output(1)))
          end
      end
      
      % Check all other sample values are zero
      for n = 2:samples
          if output(n) ~= 0 + 0j
              if channel > 1
                  error('ERROR: Channel %d output sample %d is incorrect: expected %f +j%f, actual %f + j%f\n',channel,n,0.0,0.0,real(output(1)),imag(output(1)))
              else
                  error('ERROR: output sample %d is incorrect: expected %f +j%f, actual %f + j%f\n',n,0.0,0.0,real(output(1)),imag(output(1)))
              end
          end
      end
      
      % Check if blkexp used: should be nfft
      if generics.C_HAS_BFP == 1
          if blkexp ~= nfft
              if channels > 1
                  error('ERROR: Channel %d blkexp is incorrect.  Expected value %d\n',channel,nfft)
              else
                  error('ERROR: blkexp is incorrect.  Expected value %d\n',nfft)
              end
          end
      end
      
      % Check overflow if used: scaling schedule should ensure that overflow never occurs
      if generics.C_HAS_SCALING == 1 && generics.C_HAS_BFP == 0
          if overflow == 1
              if channels > 1
                  error('ERROR: Channel %d overflow is incorrect\n',channel)
              else
                  error('ERROR: overflow is incorrect\n')
              end
          end
      end
      
      % If we got to here, simulation outputs are correct
      if channels > 1
          fprintf('Outputs from simulation of channel %d are correct\n',channel)
      else
          fprintf('Outputs from simulation are correct\n')
      end
      
  end
  
end

