function [out, g_fft, g_ifft]= fn_PRACH_IDFT_fix(input)
fn_fix_point_format_table;

input_len = length(input);

N = input_len;
switch input_len
    case 839
        FFT_len = 2048;
    case 139
        FFT_len = 512;
end

n = 0:1:(input_len-1);
n2 = 0:1:(2*input_len-1);
e_gZC = exp(-1j*pi*(n.^2)/N);
efk_fft = (1/sqrt(FFT_len))*fft(exp(1j*pi*(n2.^2)/N), FFT_len);

if 0
    e_gk = input.*e_gZC;
    e_gk_padding = [e_gk(end:-1:1), zeros(1, FFT_len - length(e_gk))];
    e_gk_fft = fft(e_gk_padding);
    
    e_gkfk_fft = e_gk_fft.*efk_fft;
    
    e_gkfk = ifft(e_gkfk_fft);
    
    out = e_gkfk((input_len-1 + 1):(2*input_len - 2 + 1));
    
    g_fft = 0;
    g_ifft = 0;
end

if 1
    
    warning('off', 'fixed:fi:overflow');
    input_fix   = fi(input, SW16F15, F_floor);
    e_gZC_fix   = fi(e_gZC, SW16F15, F_floor);
    efk_fft_fix = fi(efk_fft, SW16F14, F_floor);
    warning('on', 'fixed:fi:overflow');
    
    e_gk_fix                                                = fi(input_fix.*e_gZC_fix, SW16F15, F_floor);
    e_gk_padding_fix                                        = e_gk_fix(end:-1:1);
    e_gk_padding_fix((length(e_gk_padding_fix)+1):FFT_len)  = fi(zeros(1, FFT_len - length(e_gk_fix)), SW16F15, F_floor);
    e_gk_fft_tmp = (1/sqrt(length(e_gk_padding_fix)))*fftshift(fft(double(e_gk_padding_fix)));
    g_fft = 0;
    % [e_gk_fft_tmp, g_fft]   = fn_xilinx_FFT(double(e_gk_padding_fix), 1, log2(length(double(e_gk_padding_fix))), 3, 0, 0, 16, 16, 1, 1, 0, 0);
    e_gk_fft_fix            = fi(e_gk_fft_tmp, SW16F15, F_floor);
        
    e_gkfk_fft_fix          = fi(e_gk_fft_fix.'*efk_fft_fix, SW16F15, F_floor);
    e_gkfk_tmp = (1/sqrt(length(e_gkfk_fft_fix)))*fftshift(fft(double(e_gkfk_fft_fix)));
    g_ifft = 0;

    % [e_gkfk_tmp, g_ifft]    = fn_xilinx_FFT(double(e_gkfk_fft_fix), 0, log2(length(double(e_gkfk_fft_fix))), 3, 0, 0, 16, 16, 1, 1, 0, 0);
    e_gkfk_fix              = fi(e_gkfk_tmp.', SW16F15, F_floor);
    
    out = e_gkfk_fix((input_len - 1 + 1):(2*input_len - 2 + 1));
end

end