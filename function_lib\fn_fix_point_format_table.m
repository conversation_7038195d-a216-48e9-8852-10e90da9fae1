%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_global_fix_format_table
%
% Function:     Headfile for fix point format
%
% Input:        None
% Output:       None
%
% Modification History
% Date              Author                  Content
% 20170601          ZONG Yaozong            Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
global SW16F15;
global SW16F14;
global SW16F13;
global SW16F12;
global SW16F11;
global SW16F10;
global SW16F09;
global SW16F06;
global SW16F05;
global SW16F04;
global SW16F03;
global SW16F00

global UW16F15
global UW16F00;

global SW32F31;
global SW32F30;
global SW32F29;
global SW32F28;
global SW32F27;
global SW32F26;
global SW32F25;
global SW32F24;
global SW32F23;
global SW32F22;
global SW32F21;
global SW32F20;
global SW32F19;
global SW32F17;
global SW32F16;
global SW32F15;
global SW32F13;
global SW32F11;
global SW32F08;
global SW32F00

global UW32F32;
global UW32F31;
global UW32F30;
global UW32F27;
global UW32F26;
global UW32F23;
global UW32F21;
global UW32F20;
global UW32F15;

global SW64F52;
global SW64F30;
global SW64F29;
global SW64F00;

global UW64F30;

global F_round;
global F_floor;
