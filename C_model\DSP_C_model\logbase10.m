%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Declaration :  output = logbase10(input, input_num)
% Function :     log10
% Input :        input      - input vector Q(32,1)
%                input_num  - size of input Q(16,16)
% Output :       output     - output vector Q(32,5)
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% This routine calculates the logarithm of base 10.
% Input is an array x[SIZE] of positive Q31 integers. Valid range
% is [2^(-31), 1-2^(-31)].
% Output is an array of of integers in Q27 format.
%
% IMPLEMENTATION NOTES
% With x = m(x)*2^P(x) = m*2^P and g(x) = log10(e)*ln(1+x),
% we have that
% log10(x)=log10(e)*(ln(m)+P*ln(2))=...=g(2m-1)+(P-1)*log10(2)
% The range of 2m-1 is [0,1]. This interval is divided into two
% sub domains and g(2m-1) is approximated with a polynomial of
% degree 3 on each interval.
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%