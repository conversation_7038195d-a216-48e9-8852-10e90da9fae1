function output = fn_filtering(input, downsampling_ratio, coefficient, remove_redundancy_enable)

filter_order                            = length(coefficient);
signal_filtered_all                     = conv(input, coefficient);
start_idx                               = (filter_order+1)/2;
stop_idx                                = start_idx + length(input) - 1;
if remove_redundancy_enable == 1
    signal_filtered_without_redundancy  = signal_filtered_all(start_idx:1:stop_idx);
else
    signal_filtered_without_redundancy  = signal_filtered_all;
end
output                                  = signal_filtered_without_redundancy(1:downsampling_ratio:end);

end