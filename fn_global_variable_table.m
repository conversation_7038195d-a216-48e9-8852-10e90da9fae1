%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_global_variable_table
%
% Function:     Headfile for global variable
%
% Input:        None
% Output:       None
%
% Modification History
% --------------------
% Date      20180125
% Author    ZONG Yaozong
% Content   Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
global g_sim_para;
global g_cell_para;
global g_UE_para

global g_home_path;
global g_testvector_path;
global g_testvector_enable;
global g_progress_disp_enable;

global g_matlab_version;
global g_platforms_info;
global g_INVALID;
global g_VALID;
global g_TURE;
global g_FALSE;
global g_NA;
global g_SWITCHON;
global g_SWITCHOFF;

global g_tmp; % ��ʱ���� ��ɾ��