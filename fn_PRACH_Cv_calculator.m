%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_PRACH_Cv_calculator
%
% Function:     calculate du and Cv
%
% Input:        L_RA                - ZC sequence length
%               u                   - physical root index
%               restricted_set_cfg  - restricted set configuration
%               Ncs                 - Ncs
%               logic_root_index    - logic root index
%
% Output:       du - please refer to 3PGG TS 38.211
%               Cv - please refer to 3PGG TS 38.211
%
% Modification History
% Date      20180125
% Author    WANG Jinguo
% Content   Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function [du, Cv]= fn_PRACH_Cv_calculator(L_RA, u, restricted_set_cfg, Ncs, logic_root_index)

if restricted_set_cfg ~= 0
    if (logic_root_index < 24) || (logic_root_index >= 820)
        error('Logical root index is restricted when high speed mode,logic_root_index must be in 24~819');
    end
end

Nzc = L_RA;
q = 0;
while_flag = 1;
tmp = mod((q*u), L_RA);
while (while_flag)
    if tmp == 1
        while_flag = 0;
    else
        q = q+1;
        tmp = mod((q*u), L_RA);
    end
end

if (q<(L_RA/2))
    du = q;
else
    du = L_RA - q;
end

if restricted_set_cfg == 0
    %% unrestricted set   
    if Ncs == 0
        Cv  = 0;
    else
        v   = 0:1:(floor(L_RA/Ncs)-1); 
        Cv  = v*Ncs;
    end
    
elseif restricted_set_cfg == 1
    %% restricted typeA set
     if (du >= Ncs) && (du < L_RA/3)
         n_shift_RA  = floor(du/Ncs);
         d_start     = 2*du + n_shift_RA*Ncs;
         n_group_RA  = floor(L_RA/d_start);
         n1_shift_RA = max(floor((L_RA-2*du-n_group_RA*d_start)/Ncs), 0);
     elseif (du >= L_RA/3) && (du <= (L_RA-Ncs)/2)
         n_shift_RA  = floor((L_RA-2*du)/Ncs);
         d_start     = L_RA - 2*du + n_shift_RA*Ncs;
         n_group_RA  = floor(du/d_start);
         n1_shift_RA = min(max(floor((du-n_group_RA*d_start)/Ncs), 0), n_shift_RA);
     else
         error('Ncs does not match logical root index');
     end
     w   = n_shift_RA*n_group_RA + n1_shift_RA;
     v   = 0:1:(w-1);
     Cv  = d_start*floor(v/n_shift_RA) + mod(v, n_shift_RA)*Ncs;
elseif restricted_set_cfg == 2
    %% restricted typeB set
     if (du >= Ncs) && (du < L_RA/5)
         n_shift_RA   = floor(du/Ncs);
         d_start      = 4*du + n_shift_RA*Ncs;
         n_group_RA   = floor(L_RA/d_start);
         n1_shift_RA  = max(floor((L_RA-4*du-n_group_RA*d_start)/Ncs), 0);
         Cv_type_flag = 1;
     elseif (du >= L_RA/5) && (du <= (L_RA-Ncs)/4)
         n_shift_RA   = floor((L_RA-4*du)/Ncs);
         d_start      = L_RA - 4*du + n_shift_RA*Ncs;
         n_group_RA   = floor(du/d_start);
         n1_shift_RA  = main(max(floor((du-n_group_RA*d_start)/Ncs), 0), n_shift_RA);
         Cv_type_flag = 1;
     elseif (du >= (L_RA+Ncs)/4) && (du < 2*L_RA/7)
         n_shift_RA    = floor((4*du-L_RA)/Ncs);
         d_start       = 4*du - L_RA + n_shift_RA*Ncs;
         n_group_RA    = floor(du/d_start);
         n1_shift_RA   = max(floor((L_RA-3*du-n_group_RA*d_start)/Ncs), 0);
         n11_shift_RA  = floor(min(du-n_group_RA*d_start, 4*du-L_RA-n1_shift_RA*Ncs)/Ncs);
         n111_shift_RA = floor(((1-min(1,n1_shift_RA))*(du-n_group_RA*d_start) + min(1,n1_shift_RA)*(4*du-L_RA-n1_shift_RA*Ncs))/Ncs) - n11_shift_RA;
         d11_start     = L_RA - 3*du + n_group_RA*d_start + n1_shift_RA*Ncs;
         d111_start    = L_RA - 2*du + n_group_RA*d_start + n11_shift_RA*Ncs;
         Cv_type_flag  = 3;
     elseif (du >= 2*L_RA/7) && (du < (L_RA-Ncs)/3)
         n_shift_RA    = floor((L_RA-3*du)/Ncs);
         d_start       = L_RA - 3*du + n_shift_RA*Ncs;
         n_group_RA    = floor(du/d_start);
         n1_shift_RA   = max(floor((4*du-L_RA-n_group_RA*d_start)/Ncs), 0);
         n11_shift_RA  = floor(min(du-n_group_RA*d_start, L_RA-3*du-n1_shift_RA*Ncs)/Ncs);
         n111_shift_RA = 0;
         d11_start     = du + n_group_RA*d_start + n1_shift_RA*Ncs;
         d111_start    = 0;
         Cv_type_flag = 3;
     elseif (du >= L_RA+Ncs) && (du < 2*L_RA/5)
         n_shift_RA    = floor((3*du-Nzc)/Ncs);
         d_start       = 3*du - Nzc + n_shift_RA*Ncs;
         n_group_RA    = floor(du/d_start);
         n1_shift_RA   = max(floor((L_RA-2*du-n_group_RA*d_start)/Ncs), 0);
         n11_shift_RA  = 0;
         n111_shift_RA = 0;
         d11_start     = 0;
         d111_start    = 0;
         Cv_type_flag  = 3;
     elseif (du >= 2*L_RA/5) && (du <= (L_RA-Ncs)/2)
         n_shift_RA    = floor((Nzc-2*du)/Ncs);
         d_start       = 2*(Nzc-2*du) + n_shift_RA*Ncs;
         n_group_RA    = floor((L_RA-du)/d_start);
         n1_shift_RA   = max(floor((3*du-L_RA-n_group_RA*d_start)/Ncs), 0);
         n11_shift_RA  = 0;
         n111_shift_RA = 0;
         d11_start     = 0;
         d111_start    = 0;
         Cv_type_flag  = 3;
     end
     
     w   = n_shift_RA*n_group_RA + n1_shift_RA;
     switch Cv_type_flag
         case 1
             v      = 0:1:(w-1);
             Cv_tmp = d_start*floor(v/n_shift_RA) + mod(v, n_shift_RA)*Ncs;
         case 3
             v = 0:1:(w+n11_shift_RA+n111_shift_RA-1);
             Cv_tmp = zeros(1, w+n11_shift_RA+n111_shift_RA);
             for v_index = 0:length(v)-1
                 if (v_index >= 0) && (v_index < w)
                     Cv_tmp(1,v_index+1) = d_start*floor(v(1,v_index+1)/n_shift_RA) + mod(v(1,v_index+1), n_shift_RA)*Ncs;
                 end
                 if (v_index >= w) && (v_index < w+n11_shift_RA)
                     Cv_tmp(1,v_index+1) = d11_start + (v(1,v_index+1)-w)*Ncs;
                 end
                 if (v_index >= w+n11_shift_RA) && (v_index < w+n11_shift_RA+n111_shift_RA)
                     Cv_tmp(1,v_index+1) = d111_start + (v(1,v_index+1)-w-n11_shift_RA)*Ncs;
                 end
             end
         otherwise
             error('no such Cv case!');
     end
     Cv = Cv_tmp;
end

end