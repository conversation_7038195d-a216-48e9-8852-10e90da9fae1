function output = fn_unwrap_fix(p, cutoff, max_word_len)

if nargin<2
    cutoff = pi;
    max_word_len = 64;
end

T_fimath        = p.fimath;
p_word_len      = p.WordLength;
p_fraction_len  = p.FractionLength;
output_format   = numerictype(1, max_word_len, p_fraction_len);

int_p_fix       = double(p)*(2^p_fraction_len);

int_pi_fix      = fix(pi*(2^p_fraction_len));
int_n_pi_fix    = fix(-pi*(2^p_fraction_len));
int_2pi_fix     = fix(2*pi*(2^p_fraction_len));

int_cutoff = cutoff*(2^p_fraction_len);

T_flag = 0;
if size(int_p_fix, 2) ~= 1
    int_p_fix   = int_p_fix.';
    T_flag      = 1;
end

%LocalUnwrap   Unwraps column vector of phase values.

m = length(p);

% Unwrap phase angles.  Algorithm minimizes the incremental phase variation
% by constraining it to the range [-pi,pi]
dp = diff(int_p_fix,1,1);                          % Incremental phase variations
dps = mod(dp+int_pi_fix,int_2pi_fix) - int_pi_fix; % Equivalent phase variations in [-pi,pi)
dps(dps==int_n_pi_fix & dp>0,:) = int_pi_fix;      % Preserve variation sign for pi vs. -pi
dp_corr = dps - dp;                                % Incremental phase corrections
dp_corr(abs(dp)<int_cutoff,:) = 0;                 % Ignore correction when incr. variation is < CUTOFF

% Integrate corrections and add to P to produce smoothed phase values
int_p_fix(2:m,:) = int_p_fix(2:m,:) + cumsum(dp_corr,1);

if T_flag == 1
    int_p_fix = int_p_fix.';
end

output_tmp = int_p_fix/(2^p_fraction_len);
output     = fi(output_tmp, output_format, T_fimath);

end