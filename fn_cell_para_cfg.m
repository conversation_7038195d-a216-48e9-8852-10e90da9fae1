%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_cell_para_cfg
%
% Function:     Configure cell parameter
%
% Input:        None
% Output:       None
%
% Modification History
% Date      20180125
% Author    ZONG Yaozong, WANG Jinguo
% Content   Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

function fn_cell_para_cfg
fn_global_variable_table;

g_cell_para.frame_structure_u                   = g_sim_para.frame_structure_u;
g_cell_para.sim_bandwidth                       = g_sim_para.sim_bandwidth;

g_cell_para.N_start_BWP                         = g_sim_para.N_start_BWP;
g_cell_para.N_BWP_size                          = g_sim_para.N_BWP_size;
g_cell_para.n_start_RA                          = g_sim_para.n_start_RA;
g_cell_para.lowest_numbered_sc_k_u_0            = g_sim_para.lowest_numbered_sc_k_u_0; 

switch g_cell_para.frame_structure_u
    case 1
        g_cell_para.N_sc_per_RB             = 12;         % subcarrier number per RB
        g_cell_para.N_symbol_per_slot       = 14;         % OFDM symbol number per slot
        g_cell_para.N_slot_per_frame        = 20;         % slot number per frame
        g_cell_para.N_slot_per_subframe     = 02;         % slot number per subframe
        
        g_cell_para.SCS                     = 30*(10^3);  % subcarrier space(Hz)
        switch g_cell_para.sim_bandwidth
            case 100*(10^6)
                g_cell_para.FFT_max_size    = 4096;       % max FFT size
                g_cell_para.available_CRB_num_max= 273;   % max CRB number
            otherwise
                error('no such bandwidth');
        end
        g_cell_para.bandwidth_ratio_to_ref  = (g_cell_para.FFT_max_size*g_cell_para.SCS)/(2048*15000);
        g_cell_para.baseband_sample_rate    = g_cell_para.FFT_max_size*g_cell_para.SCS;
                
    otherwise
        error('no such u for frame structure');
end

[PRACH_para] = fn_PRACH_para_cfg(g_cell_para.frame_structure_u, g_cell_para.bandwidth_ratio_to_ref, g_sim_para.PRACH_format, ...
    g_sim_para.PRACHRootSqeuenceIndex, g_sim_para.restrictedSetConfig, g_sim_para.PRACH_zeroCorrelationZoneConfig,...
    g_cell_para.n_start_RA, g_sim_para.PRACH_freq_division_num,...
    g_cell_para.N_sc_per_RB, g_cell_para.SCS, g_sim_para.gNB_num);

g_cell_para.PRACH_para = PRACH_para;

end