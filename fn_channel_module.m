function output = fn_channel_module(input, signal_sample_rate, freq_offset)
fn_global_variable_table;

[tx_num, signal_len]           = size(input);
rx_num                         = g_sim_para.gNB_num;
no_noise_enable                = g_sim_para.no_noise_enable;
Ts                             = 1/signal_sample_rate;

for rx_idx = 0:1:(rx_num-1)
    
    signal_current_rx = zeros(1, signal_len);
    
    for tx_idx = 0:1:(tx_num-1)
        signal_current_tx = input(tx_idx+1, :);
        phase_shift_by_freq_offset = exp(1j*2*pi*freq_offset*[0:1:(length(signal_current_tx)-1)]*Ts);
        signal_current_tx_with_freq_offset = signal_current_tx.*phase_shift_by_freq_offset;
        signal_current_rx = signal_current_rx + signal_current_tx_with_freq_offset ;
    end
    
    output(rx_idx+1, :) = signal_current_rx;
end

if no_noise_enable ~= 0
    noise   = wgn(size(output,1), size(output,2), -96, 'dBm', 'complex');
    output  = output + noise;
end
