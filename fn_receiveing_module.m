%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_receiveing_module
%
% Function:     PRACH receiver
%
% Input:        signal_passed_channel - signal passed channel
% Output:       None
%
% Modification History
% Date      20180125
% Author    ZONG Yaozong
% Content   Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function fn_receiveing_module(signal_passed_channel)
fn_global_variable_table;
fn_fix_point_format_table;

TD_AGC_switch                   = g_SWITCHON;
TD_AGC_target_bit               = 13;
TD_AGC_ref_len                  = 2048;
if g_sim_para.test_mode_enable ~= 0
    TD_AGC_switch               = g_SWITCHOFF;
end

RA_downsampling_mod             = 1; % 0-ideal filter 1- float point mutilevel filter
IDFT_839_139_mod                = 1; % 0-matlab 839 idft function 1-use 2048/512 ifft
PDP_bits_num                    = 32;

PRACH_para                      = g_cell_para.PRACH_para;
PRACH_CP_len                    = PRACH_para.CP_len;
basic_seq_num                   = PRACH_para.n_root;
PRACH_L_RA                      = PRACH_para.L_RA;
sequence_rep_num                = PRACH_para.sequence_rep_num;
PRACH_Cv                        = PRACH_para.Cv;
PRACH_du                        = PRACH_para.du;
PRACH_Ncs                       = PRACH_para.Ncs;
PRACH_restricted_set_cfg        = PRACH_para.restricted_set_cfg;
PRACH_detecting_threshold_fix   = fi(PRACH_para.detecting_threshold, SW32F25, F_floor);
preamble_local_freq_domain      = PRACH_para.preamble_local_freq_domain;
RB_num_per_PRACH                = PRACH_para.RB_num_per_PRACH;
PRACH_freq_division_num         = PRACH_para.PRACH_freq_division_num; 
plot_PDP_enable                 = g_sim_para.plot_PDP_enable;
warning('off', 'fixed:fi:overflow');
preamble_local_freq_domain_fix  = fi(preamble_local_freq_domain, SW16F15, F_floor);
warning('on', 'fixed:fi:overflow');

[rx_num, signal_len]            = size(signal_passed_channel);

AGC_TD_list                     = zeros(sequence_rep_num, rx_num);
fft_exponent_list               = zeros(sequence_rep_num, rx_num);
ifft_exponent_list              = zeros(sequence_rep_num, basic_seq_num, rx_num);

correlation_result_time_domain_all_fix = fi(zeros(sequence_rep_num, basic_seq_num, rx_num, PRACH_L_RA), SW16F15, F_floor);

%% calculate correlation result
for rx_idx = 0:1:(rx_num-1)
    
    signal_current_rx       = signal_passed_channel(rx_idx+1,:); 
    
    %% half SCS freq offset
    if g_sim_para.half_SCS_freq_offset_enable == 1
        signal_current_rx = signal_current_rx.*exp(-1j*2*pi*((g_cell_para.SCS)/2)*[0:1:(length(signal_current_rx)-1)]*(1/g_cell_para.baseband_sample_rate));
    end
   
    %% remove CP
    start_idx_tmp           = PRACH_CP_len+1;
    stop_idx_tmp            = start_idx_tmp + PRACH_para.sequence_rep_num*PRACH_para.N_u - 1;
    signal_TD_without_CP    = signal_current_rx(start_idx_tmp:1:stop_idx_tmp);
    
    for seq_rep_idx = 0:1:(sequence_rep_num-1)
        start_idx_tmp   = seq_rep_idx*PRACH_para.N_u + 1;
        stop_idx_tmp    = (seq_rep_idx+1)*PRACH_para.N_u;
        signal_TD_without_CP_current_rep    = signal_TD_without_CP(start_idx_tmp:stop_idx_tmp);
        
        %% time domain AGC
        if TD_AGC_switch == g_SWITCHON
            TD_AGC_ref_signal                      = signal_TD_without_CP_current_rep(1:TD_AGC_ref_len);
            TD_AGC_ref_signal_mean_amp             = mean([abs(real(TD_AGC_ref_signal)),abs(imag(TD_AGC_ref_signal))])*(2^15);
            TD_AGC_factor                          = TD_AGC_target_bit - ceil(log2(TD_AGC_ref_signal_mean_amp));
            AGC_TD_list(seq_rep_idx+1,rx_idx+1)    = TD_AGC_factor;
            signal_TD_without_CP_after_AGC         = signal_TD_without_CP_current_rep*(2^TD_AGC_factor);
        else
            TD_AGC_factor                          = 0;
            AGC_TD_list(seq_rep_idx+1,rx_idx+1)    = TD_AGC_factor;
            signal_TD_without_CP_after_AGC         = signal_TD_without_CP_current_rep;
        end
        
        %% shift PRACH to DC 
        % 将数据搬移到零频，使用一个滤波器
        signal_TD_without_CP_in_middle = fn_PRACH_freq_shift(signal_TD_without_CP_after_AGC, PRACH_para, g_cell_para, g_cell_para.baseband_sample_rate, 0);
                
        %% downsampling
        signal_TD_downsampled = fn_RA_downsampling(signal_TD_without_CP_in_middle, PRACH_L_RA, PRACH_freq_division_num , RA_downsampling_mod);
                
        %% OFDM demodulation
        signal_FD_downsampled_tmp = (1/sqrt(length(signal_TD_downsampled)))*(fft(signal_TD_downsampled));
        blkexp = 0;
        % [signal_FD_downsampled_tmp, blkexp]            = fn_xilinx_FFT(signal_TD_downsampled, 1, log2(length(signal_TD_downsampled)), 3, 0, 0, 16, 16, 1, 1, 1, 0);
        signal_FD_downsampled                          = fftshift(signal_FD_downsampled_tmp);
        signal_FD_downsampled_fix                      = fi(signal_FD_downsampled, SW16F15, F_floor);
        fft_exponent_list(seq_rep_idx+1, rx_idx+1)     = -blkexp;
        
        %% extract preamble sequence (need a function to complete frequency demapping)
        preamble_freq_received_tmp  = fn_extract_PRACH(signal_FD_downsampled_fix, PRACH_para.L_RA);
        preamble_freq_received_fix  = fi(preamble_freq_received_tmp, SW16F15, F_floor);
        
        %% calculate correlation result
        for basic_seq_idx = 0:1:(basic_seq_num-1)
            freq_local_current_fix          = fi(preamble_local_freq_domain_fix(basic_seq_idx+1,:), SW16F15, F_floor);
            correlation_result_freq_tmp     = preamble_freq_received_fix.*freq_local_current_fix;
            correlation_result_freq_fix     = fi(correlation_result_freq_tmp, SW16F15, F_floor);

            %% ifft 
            if IDFT_839_139_mod == 0
                % int_correlation_result_time_tmp                                = fix(ifft(ifftshift(double(correlation_result_freq_fix)))*(2^15));
                int_correlation_result_time_tmp                                = fix(ifft(double(correlation_result_freq_fix))*(2^15));
                int_max_amp                                                    = max([abs(real(int_correlation_result_time_tmp)),abs(imag(int_correlation_result_time_tmp))]);
                ifft_exponent_curent                                           = 15 - ceil(log2(int_max_amp));
                ifft_exponent_list(seq_rep_idx+1, basic_seq_idx+1, rx_idx+1)   = ifft_exponent_curent;
                
                correlation_result_time_domain_fix = fi((int_correlation_result_time_tmp*(2^ifft_exponent_curent))/(2^15), SW16F15, F_floor);
                correlation_result_time_domain_all_fix(seq_rep_idx+1, basic_seq_idx+1, rx_idx+1, :) = correlation_result_time_domain_fix;
            elseif IDFT_839_139_mod == 1
                % 按照固定长度2048/512进行IFFT计算，需要进行以下变换
                [correlation_result_time_domain_flt, g_fft, g_ifft] = fn_PRACH_IDFT_fix(double(correlation_result_freq_fix));
                correlation_result_time_domain_all_fix(seq_rep_idx+1, basic_seq_idx+1, rx_idx+1, :) = fi(correlation_result_time_domain_flt, SW16F15, F_floor);
                ifft_exponent_list(seq_rep_idx+1, basic_seq_idx+1, rx_idx+1)   = -(g_fft + g_ifft);
            end
            
        end
    end
                 
end

%% combine PDP 
PDP_matrix_fix = fn_combine_PDP_fix(correlation_result_time_domain_all_fix, AGC_TD_list, fft_exponent_list, ifft_exponent_list);

%% load PDP from FPGA
% fn_FPGA_PDP_loader

%% PDP detector
if PRACH_restricted_set_cfg == 0
    switch PDP_bits_num
        case 16
            PDP_matrix_fix = fi(PDP_matrix_fix, SW16F15, F_floor);
            [RA_status_matrix, preamble_power_matrix_fix, TA_result_fix] = fn_PDP_detector_unrestricted_PDP_16_bits_fix(PDP_matrix_fix, PRACH_restricted_set_cfg, PRACH_Ncs, PRACH_Cv, PRACH_du, PRACH_detecting_threshold_fix, plot_PDP_enable);
        case 32
            [RA_status_matrix, preamble_power_matrix_fix, TA_result_fix] = fn_PDP_detector_unrestricted_fix(PDP_matrix_fix, PRACH_restricted_set_cfg, PRACH_Ncs, PRACH_Cv, PRACH_du, PRACH_detecting_threshold_fix, plot_PDP_enable);
    end
else
    switch PDP_bits_num
        case 16
            PDP_matrix_fix = fi(PDP_matrix_fix, SW16F15, F_floor);
            [RA_status_matrix, preamble_power_matrix_fix, TA_result_fix] = fn_PDP_detector_restricted_PDP_16_bits_fix(PDP_matrix_fix, PRACH_restricted_set_cfg, PRACH_Ncs, PRACH_Cv, PRACH_du, PRACH_detecting_threshold_fix, plot_PDP_enable);
        case 32
            [RA_status_matrix, preamble_power_matrix_fix, TA_result_fix] = fn_PDP_detector_restricted_fix(PDP_matrix_fix, PRACH_restricted_set_cfg, PRACH_Ncs, PRACH_Cv, PRACH_du, PRACH_detecting_threshold_fix, plot_PDP_enable);
            
    end
end

%% save simulation result

end