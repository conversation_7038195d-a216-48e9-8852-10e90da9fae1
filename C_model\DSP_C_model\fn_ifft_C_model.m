function [ifft_out_final, ifft_bitshift_num] = fn_ifft_C_model(ifft_input, ifft_size)

if ~isfi(ifft_input)
    error('input must be a fix-point object');
end

ifft_input_real = fn_fi2double(real(ifft_input));
ifft_input_imag = fn_fi2double(imag(ifft_input));
switch ifft_size
    case 275
        [ifft_output_real, iff_output_imag, ifft_bitshift_num] = IFFT275ForMatlab(ifft_input_real, ifft_input_imag);
    case 273
        [ifft_output_real, iff_output_imag, ifft_bitshift_num] = IFFT273ForMatlab(ifft_input_real, ifft_input_imag);
    case 272
        [ifft_output_real, iff_output_imag, ifft_bitshift_num] = IFFT272ForMatlab(ifft_input_real, ifft_input_imag);
    case 100
        [ifft_output_real, iff_output_imag, ifft_bitshift_num] = IFFT100ForMatlab(ifft_input_real, ifft_input_imag);
    otherwise
        error('no such ifft point number');
end
ifft_out = (ifft_output_real + 1j*iff_output_imag)/(2^15);
ifft_out_final  = fi(ifft_out, numerictype(1, 16, 15), fimath('RoundMode','floor','OverflowMode','Saturate'));


function output = fn_fi2double(input)

if isfi(input)
    output = floor(double(input)*(2^input.FractionLength));
else
    error('input must be a fix-point object');
end



