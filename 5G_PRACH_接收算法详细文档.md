# 5G PRACH接收模块算法详细文档

## 1. 算法概述

5G PRACH（Physical Random Access Channel）接收模块负责检测和处理用户设备发送的随机接入前导序列，实现用户接入检测和时间对齐估计。

### 1.1 主要功能
- 前导序列检测
- 时间对齐（TA）估计  
- 多天线接收处理
- 序列重复处理
- 功率延迟分布（PDP）计算

### 1.2 输入输出格式

**输入：**
- `S(t)` - 通过信道的接收信号，复数时域信号
- 维度：`[N_rx × N_samples]`，其中N_rx为接收天线数

**输出：**
- `D` - 检测状态矩阵，二进制矩阵 `[1 × 64]`
- `P` - 前导功率矩阵，定点数矩阵 `[1 × 64]`  
- `T` - 时间对齐结果，定点数矩阵 `[1 × 64]`

## 2. 定点数格式定义

系统采用多种定点数格式以平衡精度和硬件复杂度：

```
SW16F15: 16位有符号数，15位小数 - 用于中间计算
SW32F30: 32位有符号数，30位小数 - 用于高精度计算
SW32F25: 32位有符号数，25位小数 - 用于门限设置
SW64F30: 64位有符号数，30位小数 - 用于累加运算
```

## 3. 详细算法流程

### 3.1 初始化参数设置

```
输入参数：
- L_RA ∈ {139, 839} - PRACH序列长度
- N_rep - 序列重复次数
- N_cs - 循环移位参数
- C_v - 循环移位集合
- d_u - 序列间隔参数
- θ_th - 检测门限
```

### 3.2 接收天线循环处理

对每个接收天线 `i ∈ [0, N_rx-1]`：

#### 步骤A：半子载波频偏补偿
```
if 半SCS频偏使能 then
    S_i(n) = S_i(n) × exp(-j·2π·(SCS/2)·n/F_s)
end
```
其中：
- `SCS` - 子载波间隔
- `F_s` - 基带采样率
- `n` - 采样点索引

#### 步骤B：循环前缀移除
```
起始索引: idx_start = L_CP + 1
结束索引: idx_end = idx_start + N_rep × N_u - 1
S_noCP(n) = S_i(idx_start : idx_end)
```

### 3.3 序列重复循环处理

对每个重复序列 `k ∈ [0, N_rep-1]`：

#### 步骤C：时域AGC处理（核心算法）

**C1. 参考信号提取**
```
S_ref(n) = S_noCP(k×N_u+1 : k×N_u+L_ref)
```
其中 `L_ref = 2048` 为AGC参考长度

**C2. 平均幅度计算**
```
A_mean = mean([|Re(S_ref)|, |Im(S_ref)|]) × 2^15
```

**C3. AGC因子计算**
```
G_AGC = B_target - ⌈log₂(A_mean)⌉
```
其中 `B_target = 13` 为目标位数

**C4. 信号增益调整**
```
S_AGC(n) = S_noCP(n) × 2^G_AGC
AGC_list[k,i] = G_AGC  // 记录AGC因子
```

#### 步骤D：频域搬移处理

**D1. 计算搬移参数**
```
idx_BWP = SCS_ratio × (k_u0 + N_start×N_sc + n_start×N_sc) + k_offset
idx_DC = (N_CRB_max × N_sc × SCS_ratio / 2)
idx_target = idx_DC - ⌊L_RA/2⌋
N_shift = idx_target - idx_BWP
```

**D2. 频域搬移**
```
f_shift = N_shift × SCS_PRACH
S_shift(n) = S_AGC(n) × exp(j·2π·f_shift·n/F_s)
```

#### 步骤E：降采样处理

**E1. 确定降采样参数**
```
if L_RA = 839 then
    L_out = N_freq × 1024
else if L_RA = 139 then  
    L_out = N_freq × 256
end
R_down = L_in / L_out  // 降采样比例
```

**E2. 多级滤波降采样**
```
// CIC滤波器
S_CIC = CIC_filter(S_shift, R_1, N_stages)

// FIR滤波器级联
S_FIR1 = FIR_filter(S_CIC, h_1, R_2)
S_FIR2 = FIR_filter(S_FIR1, h_2, R_3)
S_down = S_FIR2
```

#### 步骤F：OFDM解调

**F1. FFT变换**
```
S_freq_tmp = (1/√L_down) × FFT(S_down)
S_freq = fftshift(S_freq_tmp)
```

**F2. 定点化处理**
```
S_freq_fix = quantize(S_freq, SW16F15)
FFT_exp[k,i] = -blk_exp  // 记录FFT指数
```

#### 步骤G：前导序列提取

```
idx_mid = L_freq/2 + 1
idx_start = idx_mid - ⌊L_RA/2⌋
idx_end = idx_start + L_RA - 1
R_preamble = S_freq_fix[idx_start : idx_end]
```

### 3.4 相关计算处理

对每个基本序列 `m ∈ [0, N_root-1]`：

#### 步骤H：频域相关

```
L_local = Local_seq[m,:]  // 本地序列
C_freq = R_preamble .* conj(L_local)
C_freq_fix = quantize(C_freq, SW16F15)
```

#### 步骤I：IFFT处理

**模式0（标准IFFT）：**
```
C_time_tmp = IFFT(C_freq_fix) × 2^15
A_max = max([|Re(C_time_tmp)|, |Im(C_time_tmp)|])
E_IFFT = 15 - ⌈log₂(A_max)⌉
C_time = quantize(C_time_tmp × 2^E_IFFT / 2^15, SW16F15)
IFFT_exp[k,m,i] = E_IFFT
```

**模式1（定点IFFT）：**
```
[C_time, E_FFT, E_IFFT] = PRACH_IDFT_fix(C_freq_fix)
C_time_fix = quantize(C_time, SW16F15)
IFFT_exp[k,m,i] = -(E_FFT + E_IFFT)
```

### 3.5 PDP组合和AGC补偿

#### 步骤J：AGC补偿计算

**J1. 总AGC因子计算**
```
for k = 0 to N_rep-1
    for i = 0 to N_rx-1
        G_total[k,m,i] = AGC_list[k,i] + FFT_exp[k,i] + IFFT_exp[k,m,i]
    end
end
G_ref = min(G_total)  // 参考AGC因子
```

**J2. 功率计算和累加**
```
PDP_sum = zeros(L_RA)
for k = 0 to N_rep-1
    for i = 0 to N_rx-1
        G_shift = G_ref - G_total[k,m,i]
        C_comp = C_time[k,m,i,:] × 2^G_shift
        P_temp = C_comp .* conj(C_comp)
        PDP_sum = PDP_sum + P_temp
    end
end
```

**J3. 平均处理**
```
N_acc = N_rx × N_rep
B_avg = log₂(N_acc)
PDP[m,:] = bitshift(PDP_sum, -B_avg)
```

### 3.6 检测判决处理

#### 步骤K：噪声估计

**K1. 峰值索引提取**
```
for each window w:
    [~, peak_idx] = sort(PDP[m, window_w], 'descend')
    peak_indices = peak_idx[1:4] + window_start
end
noise_indices = setdiff([1:L_RA], all_peak_indices)
```

**K2. 噪声功率计算**
```
N_samples = PDP[m, noise_indices]
N_avg = mean(N_samples)
if N_avg = 0 then
    N_avg = 5/2^15  // 最小噪声门限
end
```

#### 步骤L：检测判决

**L1. 门限计算**
```
Threshold = N_avg × θ_th
```

**L2. 峰值检测**
```
for each window w:
    P_peak = max(PDP[m, window_w])
    P_combined = P_peak + adjacent_samples
    
    if P_combined > Threshold then
        Detection[preamble_ID] = 1
        // 计算TA
        TA[preamble_ID] = peak_position + α × direction
    else
        Detection[preamble_ID] = 0
    end
end
```

## 4. 关键算法特点

### 4.1 多级定点化设计
- 不同处理阶段采用适配的定点格式
- 通过指数记录和补偿保持计算精度
- 硬件友好的位宽设计

### 4.2 自适应AGC处理  
- 基于信号统计特性的自适应增益控制
- 多天线和序列重复的一致性补偿
- 防止定点溢出的动态范围管理

### 4.3 高效降采样架构
- CIC+FIR多级滤波器设计
- 支持多种降采样比例
- 抗混叠滤波器优化

### 4.4 鲁棒检测算法
- 基于统计的噪声估计
- 自适应门限设置
- 亚样本级时间对齐估计

## 5. 性能指标

- **检测概率**: > 99% (SNR > -10dB)
- **虚警概率**: < 1%  
- **TA估计精度**: ±0.5 采样点
- **处理延迟**: < 1ms
- **硬件复杂度**: 适配FPGA实现

该算法设计充分考虑了5G PRACH系统的实际需求，通过精心的定点化设计和AGC处理，确保了系统的检测性能和硬件实现可行性。

## 6. 详细数学模型

### 6.1 AGC数学模型

**信号幅度统计模型：**
```
设接收信号为: s(n) = a(n) + jb(n)
实部幅度: |a(n)| = |Re(s(n))|
虚部幅度: |b(n)| = |Im(s(n))|
平均幅度: μ = E[|a(n)|, |b(n)|]
```

**AGC因子计算：**
```
量化幅度: μ_q = μ × 2^15
当前位数: B_current = ⌈log₂(μ_q)⌉
AGC因子: G = B_target - B_current = 13 - B_current
调整后信号: s_agc(n) = s(n) × 2^G
```

**动态范围分析：**
- 输入动态范围: [-2^15, 2^15-1]
- AGC调整范围: [-8, +8] (典型值)
- 输出动态范围: 目标13位有效位

### 6.2 频域搬移数学模型

**子载波映射关系：**
```
BWP起始子载波: k_BWP = SCS_ratio × (k_u0 + N_start×12 + n_start×12) + k_offset
DC子载波索引: k_DC = N_CRB_max × 12 × SCS_ratio / 2
目标子载波: k_target = k_DC - ⌊L_RA/2⌋
搬移子载波数: Δk = k_target - k_BWP
```

**时域频移实现：**
```
频移量: Δf = Δk × SCS_PRACH
相位旋转: φ(n) = 2π × Δf × n / F_s
搬移信号: s_shift(n) = s(n) × e^{jφ(n)}
```

### 6.3 降采样滤波器设计

**CIC滤波器传输函数：**
```
H_CIC(z) = [(1-z^(-RM))/(1-z^(-1))]^N / R^N
其中: R=12 (降采样因子), M=1 (延迟), N=3 (级数)
```

**FIR滤波器系数（96倍降采样）：**
```
h₁ = [-0.0004, 0, 0.0103, 0, 0.0586, 0, 0.2988, 0.5,
      0.2988, 0, -0.0586, 0, 0.0103, 0, -0.0004]
归一化: h₁ = h₁ / sum(h₁)
```

### 6.4 相关检测数学模型

**频域相关计算：**
```
接收前导: R(k) = FFT{r(n)}[k], k ∈ [0, L_RA-1]
本地序列: L_m(k) = 本地第m个序列的频域表示
相关结果: C_m(k) = R(k) × L_m*(k)
```

**时域相关输出：**
```
c_m(n) = IFFT{C_m(k)}
功率延迟分布: P_m(n) = |c_m(n)|²
```

### 6.5 PDP组合数学模型

**多天线多重复组合：**
```
总AGC因子: G_total[k,m,i] = G_AGC[k,i] + G_FFT[k,i] + G_IFFT[k,m,i]
参考因子: G_ref = min{G_total[k,m,i]}
补偿因子: G_comp[k,m,i] = G_ref - G_total[k,m,i]
```

**功率累加：**
```
P_sum(n) = Σ_k Σ_i |c_m[k,i](n) × 2^G_comp[k,m,i]|²
平均功率: P_avg(n) = P_sum(n) / (N_rep × N_rx)
最终PDP: PDP_m(n) = P_avg(n)
```

### 6.6 检测判决数学模型

**噪声估计：**
```
峰值索引集合: I_peak = {所有检测窗口的前4个峰值索引}
噪声索引集合: I_noise = {1,2,...,L_RA} \ I_peak
噪声样本: N_samples = {PDP_m(i) | i ∈ I_noise}
噪声功率: σ_n² = mean(N_samples)
```

**检测门限：**
```
检测门限: T_th = σ_n² × θ_th
其中 θ_th 为门限因子 (典型值: 6-10)
```

**峰值检测：**
```
对于检测窗口 w:
窗口范围: [w_start, w_end]
峰值位置: n_peak = argmax{PDP_m(n) | n ∈ [w_start, w_end]}
峰值功率: P_peak = PDP_m(n_peak)
组合功率: P_comb = P_peak + P_adjacent
```

**检测判决：**
```
if P_comb > T_th then
    检测成功: D_m,w = 1
    TA估计: TA_m,w = n_peak + α × sign(P_left - P_right)
else
    检测失败: D_m,w = 0
end
```

其中 α = 0.5 为亚样本插值因子。

## 7. 定点化实现细节

### 7.1 数值表示格式

**定点数格式定义：**
```
SW16F15: 符号位(1) + 整数位(0) + 小数位(15)
         表示范围: [-1, 1-2^(-15)]
         精度: 2^(-15) ≈ 3.05×10^(-5)

SW32F30: 符号位(1) + 整数位(1) + 小数位(30)
         表示范围: [-2, 2-2^(-30)]
         精度: 2^(-30) ≈ 9.31×10^(-10)
```

### 7.2 溢出处理策略

**饱和处理：**
```
if 计算结果 > 最大值 then
    输出 = 最大值
else if 计算结果 < 最小值 then
    输出 = 最小值
else
    输出 = 计算结果
end
```

**舍入模式：**
- `F_floor`: 向下舍入（向负无穷方向）
- 用于保证数值稳定性

### 7.3 精度分析

**量化噪声模型：**
```
量化步长: Δ = 2^(-F)，其中F为小数位数
量化噪声功率: σ_q² = Δ²/12
信噪比: SNR_q = 6.02×F + 1.76 (dB)
```

**累积误差分析：**
- AGC处理: 量化误差 < 0.1dB
- FFT/IFFT: 量化误差 < 0.2dB
- 相关计算: 量化误差 < 0.15dB
- 总体误差: < 0.5dB

## 8. 硬件实现考虑

### 8.1 计算复杂度

**乘法器需求：**
- 频域搬移: 1个复数乘法器
- 降采样滤波: 15个实数乘法器
- FFT: log₂(N)×N/2 个复数乘法器
- 相关计算: N个复数乘法器

**存储需求：**
- 输入缓存: 2×N_samples×16 bits
- 本地序列: N_root×L_RA×32 bits
- PDP存储: N_root×L_RA×32 bits

### 8.2 流水线设计

**处理流水线：**
```
Stage 1: AGC + 频域搬移 (延迟: 1 clock)
Stage 2: 降采样滤波 (延迟: filter_length clocks)
Stage 3: FFT (延迟: log₂(N) clocks)
Stage 4: 相关计算 (延迟: 1 clock)
Stage 5: IFFT (延迟: log₂(N) clocks)
Stage 6: PDP组合 (延迟: N_rep×N_rx clocks)
Stage 7: 检测判决 (延迟: N_root clocks)
```

**总处理延迟：**
```
T_total = T_filter + 2×T_FFT + T_combine + T_detect
典型值: < 1000 clock cycles @ 100MHz = 10μs
```

该详细文档涵盖了5G PRACH接收算法的所有关键技术细节，为硬件实现和性能优化提供了完整的技术参考。
