## 接收模块详细计算过程分析

### 1. 接收模块初始化

```20:25:fn_receiveing_module.m
TD_AGC_switch                   = g_SWITCHON;
TD_AGC_target_bit               = 13;
TD_AGC_ref_len                  = 2048;
if g_sim_para.test_mode_enable ~= 0
    TD_AGC_switch               = g_SWITCHOFF;
end
```

**AGC参数配置：**
- `TD_AGC_switch = g_SWITCHON`：启用时域AGC
- `TD_AGC_target_bit = 13`：目标位数为13位
- `TD_AGC_ref_len = 2048`：AGC参考信号长度为2048个样本

### 2. 定点数格式定义

系统定义了多种定点数格式：

```12:15:function_lib/fn_fix_point_format_definition.m
SW16F15     = numerictype(1, 16, 15);  % 16位有符号，15位小数
SW32F30     = numerictype(1, 32, 30);  % 32位有符号，30位小数
SW32F25     = numerictype(1, 32, 25);  % 32位有符号，25位小数
```

**格式说明：**
- `SW`：有符号（Signed）
- `16/32`：总位数
- `F15/F30/F25`：小数位数
- 例如：`SW16F15`表示16位有符号数，其中15位为小数部分

### 3. 接收天线循环处理

```55:57:fn_receiveing_module.m
for rx_idx = 0:1:(rx_num-1)
    signal_current_rx       = signal_passed_channel(rx_idx+1,:); 
```

对每个接收天线进行处理。

### 4. 半子载波间隔频偏补偿

```59:62:fn_receiveing_module.m
if g_sim_para.half_SCS_freq_offset_enable == 1
    signal_current_rx = signal_current_rx.*exp(-1j*2*pi*((g_cell_para.SCS)/2)*[0:1:(length(signal_current_rx)-1)]*(1/g_cell_para.baseband_sample_rate));
end
```

**计算过程：**
- 生成频偏补偿序列：`exp(-1j*2*pi*freq_offset*t)`
- `freq_offset = SCS/2`：半子载波间隔频偏
- 与接收信号相乘进行补偿

### 5. 循环前缀移除

```64:67:fn_receiveing_module.m
start_idx_tmp           = PRACH_CP_len+1;
stop_idx_tmp            = start_idx_tmp + PRACH_para.sequence_rep_num*PRACH_para.N_u - 1;
signal_TD_without_CP    = signal_current_rx(start_idx_tmp:1:stop_idx_tmp);
```

移除CP，保留有效数据部分。

### 6. 序列重复循环处理

```69:72:fn_receiveing_module.m
for seq_rep_idx = 0:1:(sequence_rep_num-1)
    start_idx_tmp   = seq_rep_idx*PRACH_para.N_u + 1;
    stop_idx_tmp    = (seq_rep_idx+1)*PRACH_para.N_u;
    signal_TD_without_CP_current_rep    = signal_TD_without_CP(start_idx_tmp:stop_idx_tmp);
```

对每个重复序列进行处理。

### 7. 时域AGC处理（核心算法）

```74:85:fn_receiveing_module.m
if TD_AGC_switch == g_SWITCHON
    TD_AGC_ref_signal                      = signal_TD_without_CP_current_rep(1:TD_AGC_ref_len);
    TD_AGC_ref_signal_mean_amp             = mean([abs(real(TD_AGC_ref_signal)),abs(imag(TD_AGC_ref_signal))])*(2^15);
    TD_AGC_factor                          = TD_AGC_target_bit - ceil(log2(TD_AGC_ref_signal_mean_amp));
    AGC_TD_list(seq_rep_idx+1,rx_idx+1)    = TD_AGC_factor;
    signal_TD_without_CP_after_AGC         = signal_TD_without_CP_current_rep*(2^TD_AGC_factor);
```

**AGC算法详细步骤：**

1. **参考信号提取：**
   ```matlab
   TD_AGC_ref_signal = signal_TD_without_CP_current_rep(1:TD_AGC_ref_len);
   ```
   取前2048个样本作为AGC参考信号

2. **平均幅度计算：**
   ```matlab
   TD_AGC_ref_signal_mean_amp = mean([abs(real(TD_AGC_ref_signal)),abs(imag(TD_AGC_ref_signal))])*(2^15);
   ```
   - 分别计算实部和虚部的绝对值
   - 取平均值
   - 乘以2^15进行定点化（15位小数）

3. **AGC因子计算：**
   ```matlab
   TD_AGC_factor = TD_AGC_target_bit - ceil(log2(TD_AGC_ref_signal_mean_amp));
   ```
   - 目标位数：13位
   - 当前信号幅度对应的位数：`ceil(log2(mean_amp))`
   - AGC因子 = 目标位数 - 当前位数

4. **信号增益调整：**
   ```matlab
   signal_TD_without_CP_after_AGC = signal_TD_without_CP_current_rep*(2^TD_AGC_factor);
   ```
   将信号左移AGC因子位，实现增益调整

**AGC因子记录：**
```matlab
AGC_TD_list(seq_rep_idx+1,rx_idx+1) = TD_AGC_factor;
```
记录每个序列重复和接收天线的AGC因子，用于后续补偿。

### 8. 频域搬移处理

```87:88:fn_receiveing_module.m
signal_TD_without_CP_in_middle = fn_PRACH_freq_shift(signal_TD_without_CP_after_AGC, PRACH_para, g_cell_para, g_cell_para.baseband_sample_rate, 0);
```

**频域搬移算法：**

```47:52:fn_PRACH_freq_shift.m
activated_BWP_start_sc_idx_PRACH_level = SCS_ratio*(lowest_numbered_sc_k_u_0 + N_start_BWP*N_sc_per_RB_PUSCH + n_start_RA*N_sc_per_RB_PUSCH) + PRACH_mapping_sc_offset_k1;

DC_idx                            = (available_CRB_num_max*N_sc_per_RB_PUSCH*SCS_ratio/2) - 1 + 1;
target_idx                        = DC_idx - floor(PRACH_seq_len/2);
shift_sc_num_PRACH_sc_level       = target_idx-activated_BWP_start_sc_idx_PRACH_level;
```

1. **计算PRACH在频域的位置**
2. **计算需要搬移的子载波数量**
3. **生成搬移序列：**
   ```matlab
   freq_shift = shift_sc_num_PRACH_sc_level*PRACH_para.SCS;
   sample_idx = [0:1:(length(input)-1)];
   output = input.*exp(1j*2*pi*freq_shift*sample_idx*(1/sample_rate));
   ```

### 9. 降采样处理

```90:91:fn_receiveing_module.m
signal_TD_downsampled = fn_RA_downsampling(signal_TD_without_CP_in_middle, PRACH_L_RA, PRACH_freq_division_num , RA_downsampling_mod);
```

**降采样算法：**

```8:15:fn_RA_downsampling.m
output_len_table   = [1024, 256];
if PRACH_L_RA == 839
    output_len = PRACH_freq_division_num*output_len_table(1);
elseif PRACH_L_RA == 139
    output_len = PRACH_freq_division_num*output_len_table(2);
end
input_len           = length(input);
downsampling_ratio  = input_len/output_len;
```

根据PRACH序列长度确定输出长度和降采样比例。

**多级滤波降采样：**
```35:45:fn_RA_downsampling.m
case 96
    output_1 = fn_CIC_filtering(int_signal, 12, 3, 1, 32, 1, 1);
    
    coefficient = [-0.0004, 0, 0.0103, 0, 0.0586, 0,  0.2988, 0.5, ...
        0.2988, 0, -0.0586, 0, 0.0103, 0, -0.004];
    coefficient_sum =  sum(coefficient);
    coefficient = 1/(coefficient_sum)*coefficient;
    output_2 = fn_filtering(output_1, 2, coefficient, 1);
```

1. **CIC滤波**：粗降采样
2. **FIR滤波**：精细滤波，去除混叠

### 10. OFDM解调

```93:97:fn_receiveing_module.m
signal_FD_downsampled_tmp = (1/sqrt(length(signal_TD_downsampled)))*(fft(signal_TD_downsampled));
blkexp = 0;
signal_FD_downsampled                          = fftshift(signal_FD_downsampled_tmp);
signal_FD_downsampled_fix                      = fi(signal_FD_downsampled, SW16F15, F_floor);
fft_exponent_list(seq_rep_idx+1, rx_idx+1)     = -blkexp;
```

**定点化处理：**
- 使用`SW16F15`格式（16位有符号，15位小数）
- 采用`F_floor`舍入模式（向下舍入）
- 记录FFT指数用于后续补偿

### 11. 前导序列提取

```99:101:fn_receiveing_module.m
preamble_freq_received_tmp  = fn_extract_PRACH(signal_FD_downsampled_fix, PRACH_para.L_RA);
preamble_freq_received_fix  = fi(preamble_freq_received_tmp, SW16F15, F_floor);
```

从频域信号中提取PRACH前导序列。

### 12. 相关计算

```103:107:fn_receiveing_module.m
for basic_seq_idx = 0:1:(basic_seq_num-1)
    freq_local_current_fix          = fi(preamble_local_freq_domain_fix(basic_seq_idx+1,:), SW16F15, F_floor);
    correlation_result_freq_tmp     = preamble_freq_received_fix.*freq_local_current_fix;
    correlation_result_freq_fix     = fi(correlation_result_freq_tmp, SW16F15, F_floor);
```

与本地前导序列进行频域相关计算。

### 13. IFFT定点处理

```109:125:fn_receiveing_module.m
if IDFT_839_139_mod == 0
    int_correlation_result_time_tmp                                = fix(ifft(double(correlation_result_freq_fix))*(2^15));
    int_max_amp                                                    = max([abs(real(int_correlation_result_time_tmp)),abs(imag(int_correlation_result_time_tmp))]);
    ifft_exponent_curent                                           = 15 - ceil(log2(int_max_amp));
    ifft_exponent_list(seq_rep_idx+1, basic_seq_idx+1, rx_idx+1)   = ifft_exponent_curent;
    
    correlation_result_time_domain_fix = fi((int_correlation_result_time_tmp*(2^ifft_exponent_curent))/(2^15), SW16F15, F_floor);
    correlation_result_time_domain_all_fix(seq_rep_idx+1, basic_seq_idx+1, rx_idx+1, :) = correlation_result_time_domain_fix;
elseif IDFT_839_139_mod == 1
    [correlation_result_time_domain_flt, g_fft, g_ifft] = fn_PRACH_IDFT_fix(double(correlation_result_freq_fix));
    correlation_result_time_domain_all_fix(seq_rep_idx+1, basic_seq_idx+1, rx_idx+1, :) = fi(correlation_result_time_domain_flt, SW16F15, F_floor);
    ifft_exponent_list(seq_rep_idx+1, basic_seq_idx+1, rx_idx+1)   = -(g_fft + g_ifft);
end
```

**两种IFFT模式：**

1. **模式0（标准IFFT）：**
   - 直接使用MATLAB的IFFT函数
   - 计算最大幅度并确定指数
   - 进行定点化处理

2. **模式1（定点IFFT）：**
   - 使用专门的定点IFFT函数
   - 支持839点和139点序列
   - 记录FFT和IFFT的指数

### 14. PDP组合和AGC补偿

```130:131:fn_receiveing_module.m
PDP_matrix_fix = fn_combine_PDP_fix(correlation_result_time_domain_all_fix, AGC_TD_list, fft_exponent_list, ifft_exponent_list);
```

**PDP组合算法：**

```32:40:fn_combine_PDP_fix.m
for seq_rep_idx = 0:1:(seq_rep_num-1)
    AGC_TD_current_basic_seq        = AGC_TD_list(seq_rep_idx+1, :);
    fft_exponent_current_basic_seq  = fft_exponent_list(seq_rep_idx+1, :);
    ifft_exponent_current_basic_seq = squeeze(ifft_exponent_list(seq_rep_idx+1, basic_seq_idx+1, :));
    if (size(ifft_exponent_current_basic_seq, 2) == 1)
        ifft_exponent_current_basic_seq = ifft_exponent_current_basic_seq.';
    end
    AGC_total_current_basic_seq(seq_rep_idx+1, :) = AGC_TD_current_basic_seq + fft_exponent_current_basic_seq + ifft_exponent_current_basic_seq; 
end
```

**AGC补偿计算：**

```50:58:fn_combine_PDP_fix.m
for rx_idx = 0:1:(rx_num-1)
    AGC_total_current_rx                                = AGC_TD_list(seq_rep_idx+1, rx_idx+1) + fft_exponent_list(seq_rep_idx+1, rx_idx+1) ... 
                                                          + ifft_exponent_list(seq_rep_idx+1, basic_seq_idx+1, rx_idx+1);
    AGC_shift_current_rx                                = AGC_total_ref - AGC_total_current_rx;
    correlation_result_shift_fix                        = fi(correlation_result_current_fix(rx_idx+1,:)*(2^AGC_shift_current_rx), SW16F15, F_floor);
    correlation_result_shift_power_tmp                  = fi(correlation_result_shift_fix.*conj(correlation_result_shift_fix), SW32F30, F_floor);
    
    PDP_sum_result_current_basic_seq_fix                = fi(PDP_sum_result_current_basic_seq_fix + correlation_result_shift_power_tmp, SW64F30, F_floor);
end
```

**补偿步骤：**

1. **计算总AGC因子：**
   ```matlab
   AGC_total = AGC_TD + fft_exponent + ifft_exponent
   ```

2. **计算补偿偏移：**
   ```matlab
   AGC_shift = AGC_total_ref - AGC_total_current
   ```

3. **信号补偿：**
   ```matlab
   signal_compensated = signal * (2^AGC_shift)
   ```

4. **功率计算：**
   ```matlab
   power = signal_compensated * conj(signal_compensated)
   ```

5. **多天线和序列重复累加：**
   ```matlab
   PDP_sum = PDP_sum + power
   ```

6. **平均处理：**
   ```matlab
   accumulation_num = rx_num * seq_rep_num;
   avg_shift_bit_num = log2(accumulation_num);
   PDP_final = bitshift(PDP_sum, -avg_shift_bit_num);
   ```

### 15. 检测判决

```133:145:fn_receiveing_module.m
if PRACH_restricted_set_cfg == 0
    switch PDP_bits_num
        case 16
            PDP_matrix_fix = fi(PDP_matrix_fix, SW16F15, F_floor);
            [RA_status_matrix, preamble_power_matrix_fix, TA_result_fix] = fn_PDP_detector_unrestricted_PDP_16_bits_fix(PDP_matrix_fix, PRACH_restricted_set_cfg, PRACH_Ncs, PRACH_Cv, PRACH_du, PRACH_detecting_threshold_fix, plot_PDP_enable);
        case 32
            [RA_status_matrix, preamble_power_matrix_fix, TA_result_fix] = fn_PDP_detector_unrestricted_fix(PDP_matrix_fix, PRACH_restricted_set_cfg, PRACH_Ncs, PRACH_Cv, PRACH_du, PRACH_detecting_threshold_fix, plot_PDP_enable);
    end
```

根据PDP结果进行前导检测和时间对齐估计。

## 关键定点量化特点

1. **多级定点化：** 不同处理阶段使用不同的定点格式
2. **指数补偿：** 通过记录和补偿各种指数保持精度
3. **AGC自适应：** 根据信号幅度动态调整增益
4. **硬件友好：** 所有算法都考虑硬件实现的约束

这个接收模块的设计充分考虑了5G PRACH系统的实际需求，通过精心的定点化设计和AGC处理，确保了系统的检测性能和硬件实现可行性。
