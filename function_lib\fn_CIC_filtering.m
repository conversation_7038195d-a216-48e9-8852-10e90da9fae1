function output = fn_CIC_filtering(input, downsampling_ratio, order, float_mode, bit_width, remove_redundancy_enable, no_gain_flag)

input_current = input;

for index = 1:1:order
    output_tmp       = cumsum(input_current);
    if float_mode == 1
        input_current = output_tmp;
    else
        output_mod_real  = mod(real(output_tmp), 2^bit_width);
        output_mod_imag  = mod(imag(output_tmp), 2^bit_width);
        input_current    = output_mod_real + 1j*output_mod_imag;
    end
end

signal_downsampling  = input_current(downsampling_ratio:downsampling_ratio:end);

input_current = signal_downsampling;
for index = 1:1:order
    output_tmp       = input_current - [0, input_current(1:(end-1))];
    if float_mode == 1
        input_current = output_tmp;
    else
        output_mod_real  = mod(real(output_tmp), 2^bit_width);
        output_mod_imag  = mod(imag(output_tmp), 2^bit_width);
        input_current    = output_mod_real + 1j*output_mod_imag;
    end
end

if float_mode == 1
    output = input_current;
else
    output_real_tmp = real(input_current);
    output_imag_tmp = imag(input_current);
    output_real = mod(output_real_tmp + (2^(bit_width-1)), 2^bit_width) - (2^(bit_width-1));
    output_imag = mod(output_imag_tmp + (2^(bit_width-1)), 2^bit_width) - (2^(bit_width-1));
    output = output_real + 1j*output_imag;
end


if remove_redundancy_enable == 1
    output = output(order+1:end);
end

if no_gain_flag == 1
    output = (1/(downsampling_ratio^order))*output;
end

end