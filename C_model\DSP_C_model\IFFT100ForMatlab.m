%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Declaration :         [output_real, output_imag, bitshift_num] = IFFT100ForMatlab(input_real, input_imag)
% function :            100 points ifft
% input:                input_real    -   real part of IFFT input, 1*100, register value, Q(16,1)
%                       input_imag    -   imaginary part of IFFT input, 1*100, register value, Q(16,1)
% output:               output_real   -   real part of IFFT output 1*100, register value, Q(16,1)
%                       output_imag   -   imaginary part of IFFT output 1*100, register value, Q(16,1) 
%                       bitshift_num  -   right bitshift num in IFFT process
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

