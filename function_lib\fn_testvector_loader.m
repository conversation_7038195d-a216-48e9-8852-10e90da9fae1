% Function:     Load testvector
% Input:        FileName            - file name that to be loaded
%               WordLen             - word length of real/imaginary part
%               ConvertEndianFlag   - convert endian
%               ComplementFlag      - convert complement to original
%               RealNumFlag         - all elements in testvector are real number
% Output:       ComplexVector       - complex vector
%               RealPart            - real part
%               ImagPart            - imaginary part
% History
% Author          Date          Content
% ZONG Yaozong    20170519    create file
function [ComplexVector, RealPart, ImagPart] = fn_testvector_loader(FileName, WordLen, ComplementFlag, ConvertEndianFlag, RealNumFlag)

switch nargin
    case 2
        ComplementFlag    = 1;
        ConvertEndianFlag = 0;
        RealNumFlag       = 0;
    case 3
        ConvertEndianFlag = 0;
        RealNumFlag       = 0;
    case 4
        RealNumFlag       = 0;
end

if RealNumFlag == 0
    ComplexElementLen = WordLen*2/4;
else
    ComplexElementLen = WordLen/4;
end

if strcmp(FileName(end-3:end),'.bin')
    fid = fopen(FileName);
    DataDec = fread(fid);
    fclose(fid);
    
    DataHexTmp = dec2hex(DataDec);
    if size(DataHexTmp,2)~=1
        DataHexVector = reshape(DataHexTmp.', 1, []);
        DataHex = vec2mat(DataHexVector,ComplexElementLen);
    end
    
else
    fid = fopen(FileName);
    DataDec = fscanf(fid,'%x');
    fclose(fid);
    
    DataHex = dec2hex(DataDec);
    
end

if ConvertEndianFlag == 1
    switch ComplexElementLen
        case 8
            DataHexFinal = [DataHex(:,7:8), DataHex(:,5:6), DataHex(:,3:4), DataHex(:,1:2)];
        case 16
            DataHexFinal = [DataHex(:,15:16), DataHex(:,13:14), DataHex(:,11:12), DataHex(:,9:10), ...
                DataHex(:,7:8), DataHex(:,5:6), DataHex(:,3:4), DataHex(:,1:2)];
        case 32
            DataHexFinal = [DataHex(:,31:32), DataHex(:,29:30), DataHex(:,27:28), DataHex(:,25:26),...
                DataHex(:,23:24), DataHex(:,21:22), DataHex(:,19:20), DataHex(:,17:18),...
                DataHex(:,15:16), DataHex(:,13:14), DataHex(:,11:12), DataHex(:,09:10),...
                DataHex(:,07:08), DataHex(:,05:06), DataHex(:,03:04), DataHex(:,01:02)];
        otherwise
            error('this function can not support this case');
    end
    
else
    DataHexFinal = DataHex;
end

if RealNumFlag == 0
    RealDecTemp = hex2dec(DataHexFinal(:,1:(WordLen/4)));
    ImagDecTemp = hex2dec(DataHexFinal(:,((WordLen/4)+1):(2*(WordLen/4))) );
    
    if ComplementFlag == 1
        RealDec = mod(RealDecTemp + (2^(WordLen-1)), 2^WordLen) - (2^(WordLen-1));
        ImagDec = mod(ImagDecTemp + (2^(WordLen-1)), 2^WordLen) - (2^(WordLen-1));
    else
        RealDec = RealDecTemp;
        ImagDec = ImagDecTemp;
    end
    
    ComplexVector = RealDec + 1j*ImagDec;
    
    RealPart = RealDec;
    ImagPart = ImagDec;
    
    if size(ComplexVector,1) ~= 1
        ComplexVector = ComplexVector.';
        
        RealPart = RealPart.';
        ImagPart = ImagPart.';
    end
else    
    RealDecTemp = hex2dec(DataHexFinal);
    
    if ComplementFlag == 1
        RealDec = mod(RealDecTemp + (2^(WordLen-1)), 2^WordLen) - (2^(WordLen-1));
    else
        RealDec = RealDecTemp;
    end
    
    RealPart = RealDec;
    
    if size(RealPart,1) ~= 1        
        RealPart = RealPart.';
    end
    
    ImagPart = zeros(1,length(RealPart));
    ComplexVector = RealPart + 1j*ImagPart;
end



