%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_UE_para_cfg
%
% Function:     Configure UE level parameter for each UE
%
% Input:        None
% Output:       None
%
% Modification History
% Date      20180125
% Author    ZONG Yaozong
% Content   Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function fn_UE_para_cfg
fn_global_variable_table;
fn_UE_para_definition;
UE_num = g_sim_para.UE_num;

for UE_idx = 0:1:(UE_num - 1)
    g_UE_para(UE_idx+1).snr_list                        = g_sim_para.snr_list(UE_idx+1,:);
    g_UE_para(UE_idx+1).UE_ant_num                      = g_sim_para.UE_ant_num_list(UE_idx+1,:);
    g_UE_para(UE_idx+1).UE_ID                           = 100 + UE_idx;
    g_UE_para(UE_idx+1).activated_ulphy_BWP_index       = 0;
    g_UE_para(UE_idx+1).preamble_idx                    = g_sim_para.preamble_ID_list(UE_idx+1);
    g_UE_para(UE_idx+1).PRACH_freq_division_idx         = g_sim_para.PRACH_freq_division_idx_list(UE_idx+1);  
    g_UE_para(UE_idx+1).transmission_delay              = g_sim_para.transmission_delay_list(UE_idx+1,:);
    g_UE_para(UE_idx+1).doppler_freq_offset             = g_sim_para.doppler_freq_offset(UE_idx+1,:);
end

% g_UE_para(1).snr_list                   = g_sim_para.snr_list(1,:);
% g_UE_para(1).UE_ID                      = 100;
% g_UE_para(1).preamble_idx               = 0;
% g_UE_para(1).transmission_delay         = g_sim_para.transmission_delay_list(1,:);

end