%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_PRACH_freq_shift
%
% Function:
%
% Input:        input               - input
%               freq_shift          - frequency shift, unit:Hz
%               sample_rate         - sample rate of input
%               NCO_mode_enable     - 1-digital downconversion realized by NCO
%               NCO_freq_resolution - frequency resolution in NCO
%
% Output:       output              - output
%
% Modification History
% --------------------
% Date      20180203
% Author    ZONG Yaozong
% Content   Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

function output = fn_PRACH_freq_shift(input, PRACH_para, cell_para, sample_rate, NCO_mode_enable)
global g_tmp;

figure_enable = 0;

available_CRB_num_max               = cell_para.available_CRB_num_max;
N_sc_per_RB_PUSCH                   = cell_para.N_sc_per_RB;
PRACH_freq_division_num             = PRACH_para.PRACH_freq_division_num;
RB_num_per_PRACH                    = PRACH_para.RB_num_per_PRACH;
SCS_ratio                           = cell_para.SCS/PRACH_para.SCS;

PRACH_seq_len               = cell_para.PRACH_para.L_RA; 
N_start_BWP                 = cell_para.N_start_BWP;
N_BWP_size                  = cell_para.N_BWP_size;
lowest_numbered_sc_k_u_0    = cell_para.lowest_numbered_sc_k_u_0;

n_start_RA                  = cell_para.n_start_RA;
PRACH_mapping_sc_offset_k1  = cell_para.PRACH_para.PRACH_mapping_sc_offset_k1;

if figure_enable == 1
    figure
    x_freq = fftshift(fft(input));
    x_power = x_freq.*conj(x_freq);
    test_idx = find(x_power>(max(x_power)/2));
    plot(x_freq.*conj(x_freq));
end

if NCO_mode_enable == 0
    if PRACH_freq_division_num >1
        error('current version does not support this config');
    else
        
        % activated_BWP_start_sc_idx_PRACH_level = SCS_ratio*(lowest_numbered_sc_k_u_0 + N_start_BWP*N_sc_per_RB_PUSCH + n_start_RA*N_sc_per_RB_PUSCH + PRACH_freq_division_idx*RB_num_per_PRACH*N_sc_per_RB_PUSCH) + PRACH_mapping_sc_offset_k1;
        activated_BWP_start_sc_idx_PRACH_level = SCS_ratio*(lowest_numbered_sc_k_u_0 + N_start_BWP*N_sc_per_RB_PUSCH + n_start_RA*N_sc_per_RB_PUSCH) + PRACH_mapping_sc_offset_k1;
        
        DC_idx                            = (available_CRB_num_max*N_sc_per_RB_PUSCH*SCS_ratio/2) - 1 + 1;
        target_idx                        = DC_idx - floor(PRACH_seq_len/2);
        shift_sc_num_PRACH_sc_level       = target_idx-activated_BWP_start_sc_idx_PRACH_level;
        
        freq_shift                        = shift_sc_num_PRACH_sc_level*PRACH_para.SCS;
        g_tmp.shift_sc_num_PRACH_sc_level = shift_sc_num_PRACH_sc_level; % ��ʱ���� ��ɾ��
        
        sample_idx = [0:1:(length(input)-1)];
        output = input.*exp(1j*2*pi*freq_shift*sample_idx*(1/sample_rate));
        
        if figure_enable == 1
            figure
            x_freq = fftshift(fft(output));
            x_power = x_freq.*conj(x_freq);
            test_idx = find(x_power>(max(x_power)/2));
            plot(x_power);
        end
        
    end
    
else
    
end


end