%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_sqrt_fix_v2
%
% Function:     fixed point sqrt function
%
% Input:        number           - number to be used sqrt
%               input_word_len   - word length of input number
% Output:       The result of sqrt is described as mantissa*2^exponent
%               mantissa         - mantissa part
%               exponent         - exponent part, default-unsigned 32bits
%
% Modification History
% Date              Author                  Content
% 20170601          ZONG Yaozong            Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function [mantissa, exponent] = fn_sqrt_fix_v2(number, input_word_len)

if nargin <2
    input_word_len = 32;
end

if input_word_len~=32
    error('word length of input number must be 32 bits');
end

if number == 0
    mantissa = 0;
    exponent = 0;
elseif number<0;
    error('input number must be a positive number');
else
    shift = input_word_len - ceil(log2(number));
    
    if mod(shift,2)==1
        shift = shift - 1;
    end
    
    number = bitshift(number, shift);
    
    exponent = shift/2;
    
    guess = 32768;
    
    temp = 16384;
    
    for kk = 0:1:14
        guess = guess + temp;
        square = guess * guess;
        if (square > number)
            guess = guess - temp;
        end
        temp = fix(temp / 2);
    end
    mantissa = guess;
end


end
