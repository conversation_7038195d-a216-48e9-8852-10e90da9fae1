%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_fix_format_definition
%
% Function:     Definition of fix point format
%
% Input:        None
% Output:       None
%
% Modification History
% Date              Author                  Content
% 20170601          ZONG Yaozong            Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
SW16F15     = numerictype(1, 16, 15);
SW16F14     = numerictype(1, 16, 14);
SW16F13     = numerictype(1, 16, 13);
SW16F12     = numerictype(1, 16, 12);
SW16F11     = numerictype(1, 16, 11);
SW16F10     = numerictype(1, 16, 10);
SW16F09     = numerictype(1, 16, 09);
SW16F06     = numerictype(1, 16, 06);
SW16F05     = numerictype(1, 16, 05);
SW16F04     = numerictype(1, 16, 04);
SW16F03     = numerictype(1, 16, 03);
SW16F00     = numerictype(1, 16, 00);

UW16F15     = numerictype(0, 16, 15);

SW32F31     = numerictype(1, 32, 31);
SW32F30     = numerictype(1, 32, 30);
SW32F29     = numerictype(1, 32, 29);
SW32F28     = numerictype(1, 32, 28);
SW32F27     = numerictype(1, 32, 27);
SW32F26     = numerictype(1, 32, 26);
SW32F25     = numerictype(1, 32, 25);
SW32F24     = numerictype(1, 32, 24);
SW32F23     = numerictype(1, 32, 23);
SW32F22     = numerictype(1, 32, 22);
SW32F21     = numerictype(1, 32, 21);
SW32F20     = numerictype(1, 32, 20);
SW32F19     = numerictype(1, 32, 19);
SW32F17     = numerictype(1, 32, 17);
SW32F16     = numerictype(1, 32, 16);
SW32F15     = numerictype(1, 32, 15);
SW32F13     = numerictype(1, 32, 13);
SW32F11     = numerictype(1, 32, 11);
SW32F08     = numerictype(1, 32, 08);
SW32F00     = numerictype(1, 32, 00);

UW32F32     = numerictype(0, 32, 32);
UW32F31     = numerictype(0, 32, 31);
UW32F30     = numerictype(0, 32, 30);
UW32F27     = numerictype(0, 32, 27);
UW32F26     = numerictype(0, 32, 26);
UW32F23     = numerictype(0, 32, 23);
UW32F21     = numerictype(0, 32, 21);
UW32F20     = numerictype(0, 32, 20);
UW32F15     = numerictype(0, 32, 15);

SW64F30     = numerictype(1, 64, 30);
SW64F29     = numerictype(1, 64, 29);
SW64F00     = numerictype(1, 64, 00);

UW64F30     = numerictype(0, 64, 30);

F_round     = fimath('RoundMode','round','OverflowMode','Saturate');
F_floor     = fimath('RoundMode','floor','OverflowMode','Saturate');