# 5G PRACH算法验证测试文档

## 1. 测试概述

本文档描述了5G PRACH接收算法的验证测试方法，包括功能测试、性能测试和边界条件测试。

## 2. 测试环境配置

### 2.1 仿真参数设置

```matlab
% 基本配置参数
test_config.L_RA = 839;                    % PRACH序列长度
test_config.N_rep = 1;                     % 序列重复次数
test_config.N_rx = 4;                      % 接收天线数
test_config.N_root = 64;                   % 基本序列数量
test_config.SCS = 1.25e3;                  % 子载波间隔 (Hz)
test_config.Fs = 30.72e6;                  % 采样率 (Hz)

% AGC参数
test_config.agc_target_bits = 13;          % AGC目标位数
test_config.agc_ref_length = 2048;         % AGC参考长度

% 检测参数  
test_config.detection_threshold = 6;       % 检测门限 (dB)
test_config.restricted_set = 0;            % 0-非限制集
```

### 2.2 信道模型配置

```matlab
% 信道参数
channel_config.model = 'TDL-A';            % 信道模型
channel_config.delay_spread = 300e-9;      % 延迟扩展 (s)
channel_config.doppler_freq = 5;           % 多普勒频移 (Hz)
channel_config.snr_range = [-15:5:20];     % SNR测试范围 (dB)
```

## 3. 功能验证测试

### 3.1 AGC模块测试

**测试目的：** 验证AGC算法的正确性和稳定性

**测试用例A1：标准信号AGC**
```matlab
% 输入信号
input_signal = generate_prach_signal(test_config);
input_power = [-30, -20, -10, 0, 10] dBm;  % 输入功率范围

% 测试步骤
for power_idx = 1:length(input_power)
    % 设置输入功率
    signal_scaled = scale_signal(input_signal, input_power(power_idx));
    
    % AGC处理
    [signal_agc, agc_gain] = B_agc(signal_scaled, test_config);
    
    % 验证输出
    output_bits = calculate_effective_bits(signal_agc);
    assert(abs(output_bits - 13) < 0.5, 'AGC目标位数偏差过大');
end
```

**预期结果：**
- 输出信号有效位数稳定在13±0.5位
- AGC增益范围在[-8, +8]内
- 无定点溢出现象

**测试用例A2：极限信号AGC**
```matlab
% 极小信号测试
signal_min = 1e-6 * randn(1, 10000) + 1j * 1e-6 * randn(1, 10000);
[signal_agc_min, gain_min] = B_agc(signal_min, test_config);

% 极大信号测试  
signal_max = 0.9 * randn(1, 10000) + 1j * 0.9 * randn(1, 10000);
[signal_agc_max, gain_max] = B_agc(signal_max, test_config);

% 验证边界条件
assert(gain_min <= 8, 'AGC增益超出上限');
assert(gain_max >= -8, 'AGC增益超出下限');
```

### 3.2 频域搬移测试

**测试目的：** 验证频域搬移算法的准确性

**测试用例B1：频域搬移精度**
```matlab
% 生成测试信号
freq_test = [100e3, 500e3, 1e6, 2e6];      % 测试频率
for freq_idx = 1:length(freq_test)
    % 生成单频信号
    t = (0:10000-1) / test_config.Fs;
    signal_in = exp(1j * 2 * pi * freq_test(freq_idx) * t);
    
    % 频域搬移
    signal_shifted = C_freq_shift(signal_in, test_config);
    
    % 频谱分析验证
    spectrum_in = fftshift(fft(signal_in));
    spectrum_out = fftshift(fft(signal_shifted));
    
    % 验证频移效果
    [~, peak_in] = max(abs(spectrum_in));
    [~, peak_out] = max(abs(spectrum_out));
    freq_shift_actual = (peak_out - peak_in) * test_config.Fs / length(signal_in);
    
    assert(abs(freq_shift_actual - expected_shift) < 100, '频域搬移精度不足');
end
```

### 3.3 相关计算测试

**测试目的：** 验证相关计算的正确性

**测试用例C1：理想相关测试**
```matlab
% 生成本地序列
local_seq = generate_zadoff_chu_sequence(test_config.L_RA, 1);

% 理想接收信号（无噪声）
received_seq = local_seq;

% 相关计算
[corr_result, ifft_exp] = G_correlation(received_seq, local_seq, test_config);

% 验证相关峰值
[peak_value, peak_pos] = max(abs(corr_result));
assert(peak_pos == 1, '相关峰值位置错误');
assert(peak_value > 0.95, '相关峰值幅度不足');
```

## 4. 性能测试

### 4.1 检测性能测试

**测试用例D1：检测概率vs SNR**
```matlab
snr_list = -15:2:20;                       % SNR范围 (dB)
num_trials = 1000;                         % 蒙特卡洛次数
detection_prob = zeros(size(snr_list));

for snr_idx = 1:length(snr_list)
    detection_count = 0;
    
    for trial = 1:num_trials
        % 生成信号
        signal_clean = generate_prach_signal(test_config);
        signal_noisy = add_awgn(signal_clean, snr_list(snr_idx));
        
        % 完整接收处理
        [status, power, ta] = prach_receiver(signal_noisy, test_config);
        
        % 统计检测结果
        if any(status == 1)
            detection_count = detection_count + 1;
        end
    end
    
    detection_prob(snr_idx) = detection_count / num_trials;
end

% 性能要求验证
snr_10db_idx = find(snr_list == -10);
assert(detection_prob(snr_10db_idx) > 0.99, '检测概率不满足要求');
```

**预期结果：**
- SNR = -10dB时，检测概率 > 99%
- SNR = 0dB时，检测概率 > 99.9%
- 检测概率曲线平滑单调

**测试用例D2：虚警概率测试**
```matlab
num_trials = 10000;                        % 大样本测试
false_alarm_count = 0;

for trial = 1:num_trials
    % 生成纯噪声信号
    noise_signal = sqrt(0.5) * (randn(test_config.N_rx, 100000) + ...
                                1j * randn(test_config.N_rx, 100000));
    
    % 接收处理
    [status, ~, ~] = prach_receiver(noise_signal, test_config);
    
    % 统计虚警
    if any(status == 1)
        false_alarm_count = false_alarm_count + 1;
    end
end

false_alarm_prob = false_alarm_count / num_trials;
assert(false_alarm_prob < 0.01, '虚警概率超出要求');
```

### 4.2 时间对齐精度测试

**测试用例E1：TA估计精度**
```matlab
ta_offset_list = -10:0.5:10;               % TA偏移范围 (采样点)
ta_error = zeros(size(ta_offset_list));

for offset_idx = 1:length(ta_offset_list)
    ta_true = ta_offset_list(offset_idx);
    ta_estimates = [];
    
    for trial = 1:100
        % 生成带TA偏移的信号
        signal_delayed = generate_delayed_prach(test_config, ta_true);
        signal_noisy = add_awgn(signal_delayed, 0);  % 0dB SNR
        
        % TA估计
        [status, ~, ta_est] = prach_receiver(signal_noisy, test_config);
        
        if any(status == 1)
            detected_idx = find(status == 1, 1);
            ta_estimates = [ta_estimates, ta_est(detected_idx)];
        end
    end
    
    % 计算估计误差
    if ~isempty(ta_estimates)
        ta_error(offset_idx) = std(ta_estimates - ta_true);
    end
end

% 精度要求验证
assert(max(ta_error) < 0.5, 'TA估计精度不满足要求');
```

## 5. 边界条件测试

### 5.1 多径信道测试

**测试用例F1：多径环境性能**
```matlab
% 多径信道配置
multipath_delays = [0, 50e-9, 150e-9, 300e-9] * test_config.Fs;  % 延迟 (采样点)
multipath_gains = [0, -3, -6, -10];                               % 增益 (dB)

% 生成多径信道
channel = generate_multipath_channel(multipath_delays, multipath_gains);

% 测试不同SNR下的性能
snr_test = [-10, -5, 0, 5, 10];
for snr_idx = 1:length(snr_test)
    % 信号通过多径信道
    signal_clean = generate_prach_signal(test_config);
    signal_multipath = conv(signal_clean, channel);
    signal_noisy = add_awgn(signal_multipath, snr_test(snr_idx));
    
    % 性能测试
    [status, power, ta] = prach_receiver(signal_noisy, test_config);
    
    % 记录性能指标
    performance.detection_rate(snr_idx) = sum(status) / length(status);
    performance.ta_accuracy(snr_idx) = calculate_ta_accuracy(ta, true_ta);
end
```

### 5.2 频偏容忍性测试

**测试用例G1：频偏影响测试**
```matlab
freq_offset_list = [-2000:500:2000];       % 频偏范围 (Hz)
detection_rate = zeros(size(freq_offset_list));

for freq_idx = 1:length(freq_offset_list)
    freq_offset = freq_offset_list(freq_idx);
    success_count = 0;
    
    for trial = 1:100
        % 生成带频偏的信号
        signal_clean = generate_prach_signal(test_config);
        signal_offset = apply_frequency_offset(signal_clean, freq_offset, test_config.Fs);
        signal_noisy = add_awgn(signal_offset, 0);  % 0dB SNR
        
        % 检测测试
        [status, ~, ~] = prach_receiver(signal_noisy, test_config);
        
        if any(status == 1)
            success_count = success_count + 1;
        end
    end
    
    detection_rate(freq_idx) = success_count / 100;
end

% 频偏容忍性验证
freq_1khz_idx = find(abs(freq_offset_list) <= 1000);
assert(min(detection_rate(freq_1khz_idx)) > 0.95, '频偏容忍性不足');
```

## 6. 定点化验证测试

### 6.1 定点精度测试

**测试用例H1：浮点vs定点对比**
```matlab
% 生成测试信号
test_signal = generate_prach_signal(test_config);

% 浮点处理
[status_float, power_float, ta_float] = prach_receiver_float(test_signal, test_config);

% 定点处理
[status_fixed, power_fixed, ta_fixed] = prach_receiver_fixed(test_signal, test_config);

% 精度对比
power_error = abs(power_fixed - power_float) ./ abs(power_float);
ta_error = abs(ta_fixed - ta_float);

assert(max(power_error) < 0.05, '功率估计定点误差过大');
assert(max(ta_error) < 0.1, 'TA估计定点误差过大');
```

## 7. 测试报告模板

### 7.1 测试结果汇总

```
测试项目                    | 测试结果 | 要求指标 | 状态
---------------------------|----------|----------|------
AGC目标位数精度             | ±0.3位   | ±0.5位   | PASS
频域搬移精度               | <50Hz    | <100Hz   | PASS  
检测概率 (SNR=-10dB)       | 99.2%    | >99%     | PASS
虚警概率                   | 0.8%     | <1%      | PASS
TA估计精度                 | ±0.4采样点| ±0.5采样点| PASS
多径环境检测率             | 97.5%    | >95%     | PASS
频偏容忍性 (±1kHz)         | 96.8%    | >95%     | PASS
定点化功率误差             | 3.2%     | <5%      | PASS
定点化TA误差               | 0.08采样点| <0.1采样点| PASS
```

### 7.2 性能曲线

建议绘制以下性能曲线：
1. 检测概率 vs SNR曲线
2. TA估计误差 vs SNR曲线  
3. 频偏容忍性曲线
4. 多径环境性能曲线

该测试文档为5G PRACH算法的全面验证提供了系统性的测试方法和评估标准。
