function [freq_mapping_pattern_all, freq_shift]= fn_freq_mapping_pattern_generator(g_cell_para, UE_info)

N_sc_per_RB_PUSCH           = g_cell_para.N_sc_per_RB;
PUSCH_SCS                   = g_cell_para.SCS;            % subcarrier space(Hz)
FFT_max_size                = g_cell_para.FFT_max_size;        % max FFT size
available_CRB_num_max       = g_cell_para.available_CRB_num_max ;    % max RB number

N_start_BWP                 = g_cell_para.N_start_BWP;
N_BWP_size                  = g_cell_para.N_BWP_size;
lowest_numbered_sc_k_u_0    = g_cell_para.lowest_numbered_sc_k_u_0;

preamble_SCS                = g_cell_para.PRACH_para.SCS;
SCS_ratio                   = PUSCH_SCS/preamble_SCS;

n_start_RA                  = g_cell_para.n_start_RA;
PRACH_mapping_sc_offset_k1  = g_cell_para.PRACH_para.PRACH_mapping_sc_offset_k1;
PRACH_freq_division_idx     = UE_info.PRACH_freq_division_idx;

preamble_len                = g_cell_para.PRACH_para.L_RA;
available_sc_num_PRACH_level= available_CRB_num_max*N_sc_per_RB_PUSCH*SCS_ratio;% available subcarriers number in PRACH subcarrier space level
sc_per_RB_PRACH_level       = N_sc_per_RB_PUSCH*SCS_ratio;                 % subcarriers number per RB in PRACH subcarrier space level
RB_num_per_PRACH            = ceil(preamble_len/sc_per_RB_PRACH_level);    % RB number of PRACH
guard_sc_number_PRACH_level = FFT_max_size*SCS_ratio - available_sc_num_PRACH_level;

PRACH_seq_start_sc_idx_PRACH_level    = SCS_ratio*(lowest_numbered_sc_k_u_0 + N_start_BWP*N_sc_per_RB_PUSCH + n_start_RA*N_sc_per_RB_PUSCH + PRACH_freq_division_idx*RB_num_per_PRACH*N_sc_per_RB_PUSCH) + PRACH_mapping_sc_offset_k1;
PRACH_seq_stop_sc_idx_PRACH_level     = PRACH_seq_start_sc_idx_PRACH_level + preamble_len - 1;

activated_BWP_start_sc_idx_PRACH_level= N_start_BWP*sc_per_RB_PRACH_level;
activated_BWP_stop_sc_idx_PRACH_level = activated_BWP_start_sc_idx_PRACH_level + N_BWP_size*sc_per_RB_PRACH_level - 1;

freq_pattern                = ones(1, available_sc_num_PRACH_level);
freq_pattern((activated_BWP_start_sc_idx_PRACH_level+1):(activated_BWP_stop_sc_idx_PRACH_level+1)) = 2;
freq_pattern((PRACH_seq_start_sc_idx_PRACH_level+1):(PRACH_seq_stop_sc_idx_PRACH_level+1))         = 3;       

freq_mapping_pattern_all    = [zeros(1, guard_sc_number_PRACH_level/2), freq_pattern, zeros(1, guard_sc_number_PRACH_level/2)];

end