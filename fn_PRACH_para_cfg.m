function PRACH_para = fn_PRACH_para_cfg(u, bandwidth_ratio_to_ref, PRACH_format, logic_root_index, restricted_set_cfg, ZCZ_config, n_start_RA, PRACH_freq_division_num, PUSCH_N_sc_per_RB, PUSCH_SCS, RX_num)
NA = -999999; % not applicable
preamble_num_per_cell = 64;

PRACH_para.restricted_set_cfg               = restricted_set_cfg;
PRACH_para.PRACH_format                     = PRACH_format;                       % PRACH format
PRACH_para.PRACHRootSqeuenceIndex           = logic_root_index;                   % logic root index of PRACH sequence, higher-layer parameter
PRACH_para.PRACH_zeroCorrelationZoneConfig  = ZCZ_config;                         % zeros correlation zone configration, higher-layer parameter
PRACH_para.n_start_RA                       = n_start_RA;
PRACH_para.PRACH_freq_division_num          = PRACH_freq_division_num;

%% 3PGG TS 38.211 V15.0.0(2017-12) Table 6.3.3.1-1 & Table 6.3.3.1-2 PRACH preamble formats
if strcmp(PRACH_format, '0')
    TS_len_ref                     = 30720;
    PRACH_para.L_RA                = 839;
    PRACH_para.SCS                 = 1.25*(10^3);
    PRACH_para.N_u                 = 24576*bandwidth_ratio_to_ref;
    PRACH_para.sequence_rep_num    = 1;
    PRACH_para.CP_len              = 3168*bandwidth_ratio_to_ref;
    PRACH_para.GP_len              = 2976*bandwidth_ratio_to_ref;
    PRACH_para.TS_num              = ceil((PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.CP_len + PRACH_para.GP_len)/(TS_len_ref*bandwidth_ratio_to_ref));
    PRACH_para.PRACH_total_len     = PRACH_para.CP_len + PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.GP_len;
end

if strcmp(PRACH_format, '1')
    TS_len_ref                     = 30720;
    PRACH_para.L_RA                = 839;
    PRACH_para.SCS                 = 1.25*(10^3);
    PRACH_para.N_u                 = 24576*bandwidth_ratio_to_ref;
    PRACH_para.sequence_rep_num    = 2;
    PRACH_para.CP_len              = 21024*bandwidth_ratio_to_ref;
    PRACH_para.GP_len              = 21904*bandwidth_ratio_to_ref;
    PRACH_para.TS_num              = ceil((PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.CP_len + PRACH_para.GP_len)/(TS_len_ref*bandwidth_ratio_to_ref));
    PRACH_para.PRACH_total_len     = PRACH_para.CP_len + PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.GP_len;
end

if strcmp(PRACH_format, '2')
    TS_len_ref                     = 30720;
    PRACH_para.L_RA                = 839;
    PRACH_para.SCS                 = 1.25*(10^3);
    PRACH_para.N_u                 = 24576*bandwidth_ratio_to_ref;
    PRACH_para.sequence_rep_num    = 4;
    PRACH_para.CP_len              = 4688*bandwidth_ratio_to_ref;
    PRACH_para.GP_len              = 4528*bandwidth_ratio_to_ref;
    PRACH_para.TS_num              = ceil((PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.CP_len + PRACH_para.GP_len)/(TS_len_ref*bandwidth_ratio_to_ref));
    PRACH_para.PRACH_total_len     = PRACH_para.CP_len + PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.GP_len;
end

if strcmp(PRACH_format, '3')
    TS_len_ref                     = 30720;
    PRACH_para.L_RA                = 839;
    PRACH_para.SCS                 = 1.25*(10^3);
    PRACH_para.N_u                 = 6144*bandwidth_ratio_to_ref;
    PRACH_para.sequence_rep_num    = 2;
    PRACH_para.CP_len              = 3168*bandwidth_ratio_to_ref;
    PRACH_para.GP_len              = 2976*bandwidth_ratio_to_ref;
    PRACH_para.TS_num              = ceil((PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.CP_len + PRACH_para.GP_len)/(TS_len_ref*bandwidth_ratio_to_ref));
    PRACH_para.PRACH_total_len     = PRACH_para.CP_len + PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.GP_len;
end

if strcmp(PRACH_format, 'A1')
    TS_len_ref                     = 30720;
    PRACH_para.L_RA                = 139;
    PRACH_para.SCS                 = 15*(10^3)*(2^u);
    PRACH_para.N_u                 = 2048*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.sequence_rep_num    = 2;
    PRACH_para.CP_len              = 288*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.GP_len              = 0;
    PRACH_para.TS_num              = ceil((PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.CP_len + PRACH_para.GP_len)/(TS_len_ref*bandwidth_ratio_to_ref));
    PRACH_para.PRACH_total_len     = PRACH_para.CP_len + PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.GP_len;
end

if strcmp(PRACH_format, 'A2')
    TS_len_ref                     = 30720;
    PRACH_para.L_RA                = 139;
    PRACH_para.SCS                 = 15*(10^3)*(2^u);
    PRACH_para.N_u                 = 2048*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.sequence_rep_num    = 4;
    PRACH_para.CP_len              = 576*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.GP_len              = 0;
    PRACH_para.TS_num              = ceil((PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.CP_len + PRACH_para.GP_len)/(TS_len_ref*bandwidth_ratio_to_ref));
    PRACH_para.PRACH_total_len     = PRACH_para.CP_len + PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.GP_len;
end

if strcmp(PRACH_format, 'A3')
    TS_len_ref                     = 30720;
    PRACH_para.L_RA                = 139;
    PRACH_para.SCS                 = 15*(10^3)*(2^u);
    PRACH_para.N_u                 = 2048*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.sequence_rep_num    = 6;
    PRACH_para.CP_len              = 864*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.GP_len              = 0;
    PRACH_para.TS_num              = ceil((PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.CP_len + PRACH_para.GP_len)/(TS_len_ref*bandwidth_ratio_to_ref));
    PRACH_para.PRACH_total_len     = PRACH_para.CP_len + PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.GP_len;
end

if strcmp(PRACH_format, 'B1')
    TS_len_ref                     = 30720;
    PRACH_para.L_RA                = 139;
    PRACH_para.SCS                 = 15*(10^3)*(2^u);
    PRACH_para.N_u                 = 2048*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.sequence_rep_num    = 2;
    PRACH_para.CP_len              = 216*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.GP_len              = 72*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.TS_num              = ceil((PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.CP_len + PRACH_para.GP_len)/(TS_len_ref*bandwidth_ratio_to_ref));
    PRACH_para.PRACH_total_len     = PRACH_para.CP_len + PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.GP_len;
end

if strcmp(PRACH_format, 'B2')
    TS_len_ref                     = 30720;
    PRACH_para.L_RA                = 139;
    PRACH_para.SCS                 = 15*(10^3)*(2^u);
    PRACH_para.N_u                 = 2048*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.sequence_rep_num    = 4;
    PRACH_para.CP_len              = 360*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.GP_len              = 216*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.TS_num              = ceil((PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.CP_len + PRACH_para.GP_len)/(TS_len_ref*bandwidth_ratio_to_ref));
    PRACH_para.PRACH_total_len     = PRACH_para.CP_len + PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.GP_len;
end

if strcmp(PRACH_format, 'B3')
    TS_len_ref                     = 30720;
    PRACH_para.L_RA                = 139;
    PRACH_para.SCS                 = 15*(10^3)*(2^u);
    PRACH_para.N_u                 = 2048*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.sequence_rep_num    = 6;
    PRACH_para.CP_len              = 504*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.GP_len              = 360*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.TS_num              = ceil((PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.CP_len + PRACH_para.GP_len)/(TS_len_ref*bandwidth_ratio_to_ref));
    PRACH_para.PRACH_total_len     = PRACH_para.CP_len + PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.GP_len;
end

if strcmp(PRACH_format, 'B4')
    TS_len_ref                     = 30720;
    PRACH_para.L_RA                = 139;
    PRACH_para.SCS                 = 15*(10^3)*(2^u);
    PRACH_para.N_u                 = 2048*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.sequence_rep_num    = 12;
    PRACH_para.CP_len              = 936*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.GP_len              = 792*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.TS_num              = ceil((PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.CP_len + PRACH_para.GP_len)/(TS_len_ref*bandwidth_ratio_to_ref));
    PRACH_para.PRACH_total_len     = PRACH_para.CP_len + PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.GP_len;
end

if strcmp(PRACH_format, 'C0')
    TS_len_ref                     = 30720;
    PRACH_para.L_RA                = 139;
    PRACH_para.SCS                 = 15*(10^3)*(2^u);
    PRACH_para.N_u                 = 2048*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.sequence_rep_num    = 1;
    PRACH_para.CP_len              = 1240*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.GP_len              = 0;
    PRACH_para.TS_num              = ceil((PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.CP_len + PRACH_para.GP_len + PRACH_para.GP_len)/(TS_len_ref*bandwidth_ratio_to_ref));
    PRACH_para.PRACH_total_len     = PRACH_para.CP_len + PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.GP_len;
end

if strcmp(PRACH_format, 'C1')
    TS_len_ref                     = 30720;
    PRACH_para.L_RA                = 139;
    PRACH_para.SCS                 = 15*(10^3)*(2^u);
    PRACH_para.N_u                 = 2048*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.sequence_rep_num    = 4;
    PRACH_para.CP_len              = 2048*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.GP_len              = 2916*(2^-u)*bandwidth_ratio_to_ref;
    PRACH_para.TS_num              = ceil((PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.CP_len + PRACH_para.GP_len)/(TS_len_ref*bandwidth_ratio_to_ref));
    PRACH_para.PRACH_total_len     = PRACH_para.CP_len + PRACH_para.sequence_rep_num*PRACH_para.N_u + PRACH_para.GP_len;
end

SCS_ratio                       = PUSCH_SCS/PRACH_para.SCS;
sc_per_RB_PRACH_level           = PUSCH_N_sc_per_RB*SCS_ratio;
RB_num_per_PRACH                = ceil((PRACH_para.L_RA/sc_per_RB_PRACH_level));
PRACH_para.SCS_ratio            = SCS_ratio;
PRACH_para.RB_num_per_PRACH     = RB_num_per_PRACH;

%%  3PGG TS 38.211 V15.1.0 Table 6.3.3.2-1: Supported combinations of SCS of RA and SCS of PUSCH , and the corresponding value of k1  
if PRACH_para.L_RA == 839
    if PRACH_para.SCS == 1.25*(10^3)
        switch RB_num_per_PRACH
            case 6
                PRACH_para.PRACH_mapping_sc_offset_k1 = 7;
            case 3
                PRACH_para.PRACH_mapping_sc_offset_k1 = 1;
            case 2
                PRACH_para.PRACH_mapping_sc_offset_k1 = 133;
        end
        
    elseif PRACH_para.SCS == 15*(10^3)
        switch RB_num_per_PRACH
            case 24
                PRACH_para.PRACH_mapping_sc_offset_k1 = 12;
            case 12
                PRACH_para.PRACH_mapping_sc_offset_k1 = 10;
            case 6
                PRACH_para.PRACH_mapping_sc_offset_k1 = 7;
        end
    end
    
elseif PRACH_para.L_RA == 139
    PRACH_para.PRACH_mapping_sc_offset_k1 = 2;
end

%% 3PGG TS 38.211 V15.0.0(2017-12) Table 6.3.3.1-3 ~ Table 6.3.3.1-4 Mapping form PRACHRootSequenceIndex i to sequence number u for preamble
logic_root_index_mapping_table_839 = ...
    [129 710 140 699 120 719 210 629 168 671 84 755 105 734 93 746 70 769 60 779 2 837 1 838 ...
    56 783 112 727 148 691 80 759 42 797 40 799 35 804 73 766 146 693 ...
    31 808 28 811 30 809 27 812 29 810 24 815 48 791 68 771 74 765 178 661 136 703 ...
    86 753 78 761 43 796 39 800 20 819 21 818 95 744 202 637 190 649 181 658 137 702 125 714 151 688 ...
    217 622 128 711 142 697 122 717 203 636 118 721 110 729 89 750 103 736 61 778 55 784 15 824 14 825 ...
    12 827 23 816 34 805 37 802 46 793 207 632 179 660 145 694 130 709 223 616 ...
    228 611 227 612 132 707 133 706 143 696 135 704 161 678 201 638 173 666 106 733 83 756 91 748 66 773 53 786 10 829 9 830 ...
    7 832 8 831 16 823 47 792 64 775 57 782 104 735 101 738 108 731 208 631 184 655 197 642 191 648 121 718 141 698 149 690 216 623 218 621 ...
    152 687 144 695 134 705 138 701 199 640 162 677 176 663 119 720 158 681 164 675 174 665 171 668 170 669 87 752 169 670 88 751 107 732 81 758 82 757 100 739 98 741 71 768 59 780 65 774 50 789 49 790 26 813 17 822 13 826 6 833 ...
    5 834 33 806 51 788 75 764 99 740 96 743 97 742 166 673 172 667 175 664 187 652 163 676 185 654 200 639 114 725 189 650 115 724 194 645 195 644 192 647 182 657 157 682 156 683 211 628 154 685 123 716 139 700 212 627 153 686 213 626 215 624 150 689 ...
    225 614 224 615 221 618 220 619 127 712 147 692 124 715 193 646 205 634 206 633 116 723 160 679 186 653 167 672 79 760 85 754 77 762 92 747 58 781 62 777 69 770 54 785 36 803 32 807 25 814 18 821 11 828 4 835 ...
    3 836 19 820 22 817 41 798 38 801 44 795 52 787 45 794 63 776 67 772 72 767 76 763 94 745 102 737 90 749 109 730 165 674 111 728 209 630 204 635 117 722 188 651 159 680 198 641 113 726 183 656 180 659 177 662 196 643 155 684 214 625 126 713 131 708 219 620 222 617 226 613 ...
    230 609 232 607 262 577 252 587 418 421 416 423 413 426 411 428 376 463 395 444 283 556 285 554 379 460 390 449 363 476 384 455 388 451 386 453 361 478 387 452 360 479 310 529 354 485 328 511 315 524 337 502 349 490 335 504 324 515 ...
    323 516 320 519 334 505 359 480 295 544 385 454 292 547 291 548 381 458 399 440 380 459 397 442 369 470 377 462 410 429 407 432 281 558 414 425 247 592 277 562 271 568 272 567 264 575 259 580 ...
    237 602 239 600 244 595 243 596 275 564 278 561 250 589 246 593 417 422 248 591 394 445 393 446 370 469 365 474 300 539 299 540 364 475 362 477 298 541 312 527 313 526 314 525 353 486 352 487 343 496 327 512 350 489 326 513 319 520 332 507 333 506 348 491 347 492 322 517 ...
    330 509 338 501 341 498 340 499 342 497 301 538 366 473 401 438 371 468 408 431 375 464 249 590 269 570 238 601 234 605 ...
    257 582 273 566 255 584 254 585 245 594 251 588 412 427 372 467 282 557 403 436 396 443 392 447 391 448 382 457 389 450 294 545 297 542 311 528 344 495 345 494 318 521 331 508 325 514 321 518 ...
    346 493 339 500 351 488 306 533 289 550 400 439 378 461 374 465 415 424 270 569 241 598 ...
    231 608 260 579 268 571 276 563 409 430 398 441 290 549 304 535 308 531 358 481 316 523 ...
    293 546 288 551 284 555 368 471 253 586 256 583 263 576 ...
    242 597 274 565 402 437 383 456 357 482 329 510 317 522 307 532 286 553 287 552 266 573 261 578 ...
    236 603 303 536 356 483 355 484 405 434 404 435 406 433 235 604 267 572 302 537 ...
    309 530 265 574 233 606 367 472 296 543 336 503 305 534 373 466 280 559 279 560 419 420 240 599 258 581 229 610 ];

logic_root_index_mapping_table_139 = ...
    [ 1	138	2	137	3	136	4	135	5	134	6	133	7	132	8	131	9	130	10	129 ...
    11	128	12	127	13	126	14	125	15	124	16	123	17	122	18	121	19	120	20	119 ...
    21	118	 22	117	23	116	24	115	25	114	26	113	27	112	28	111	29	110	30	109 ...
    31 108  32 107 33 106  34  105 35  104 36  103 37  102 38  101 39  100 40  99 ...
    41	98	 42	97	43	96	44	95	45	94	46	93	47	92	48	91	49	90	50	89 ...
    51	88	 52	87	53	86	54	85	55	84	56	83	57	82	58	81	59	80	60	79 ...
    61	78	 62	77	63	76	64	75	65	74	66	73	67	72	68	71	69	70];
switch PRACH_para.L_RA
    case 839
        phy_root_index_u = logic_root_index_mapping_table_839(logic_root_index+1);
    case 139
        phy_root_index_u = logic_root_index_mapping_table_139(logic_root_index+1);
    otherwise
        error('no such preable length');
end

%% 3PGG TS 38.211 V15.0.0(2017-12) Table 6.3.3.1-5 ~ Table 6.3.3.1-7 Ncs for preamble formats
Ncs_cfg_table_sc_1250 = [000 013 015 018 022 026 032 038 046 059 076 093 119 167  279  419;
    015 018 022 026 032 038 046 055 068 082 100 128 158 202  237  NA;
    015 018 022 026 032 038 046 055 068 082 100 118 137 NA   NA   NA].';

Ncs_cfg_table_sc_5000 = [000 013 026 033 038 041 049 055 064 076 093 119 139 209 279  419;
    036 057 072 081 089 094 103 112 121 132 137 152 173 195 216  237;
    036 057 060 063 065 068 071 077 081 085 097 109 122 137 NA   NA].';

Ncs_cfg_table_sc_15muti2u = [0 2 4 6 8 10 12 13 15 17 19 23 27 34 46 69].';

if PRACH_para.SCS == 1250
    Ncs = Ncs_cfg_table_sc_1250(ZCZ_config+1, restricted_set_cfg+1);
elseif PRACH_para.SCS == 5000
    Ncs = Ncs_cfg_table_sc_5000(ZCZ_config+1, restricted_set_cfg+1);
else
    Ncs = Ncs_cfg_table_sc_15muti2u(ZCZ_config+1, restricted_set_cfg+1);
end

if restricted_set_cfg ~=0
    restricted_set_Ncs_check_table = [NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,...
        15,15,15,15,15,15,18,18,18,18,18,18,22,22,22,22,22,22,26,26,26,26,26,26,26,26,26,26,32,32,32,32,32,32,32,32,32,32,32,32,38,38,38,38,38,38,38,38,38,38,38,38,46,46,46,46,46,46,46,46,46,46,46,46,46,46,...
        55,55,55,55,55,55,55,55,55,55,55,55,55,55,55,55,55,55,55,55,55,55,55,55,55,55,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,...
        100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,...
        158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,...
        202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,...
        237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,237,...
        202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,...
        158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,...
        128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,...
        82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,68,55,55,55,55,55,55,55,55,55,55,55,55,55,55,...
        46,46,46,46,46,46,46,46,46,46,46,46,38,38,38,38,38,38,38,38,38,38,38,38,32,32,32,32,32,32,26,26,26,26,26,26,26,26,22,22,22,22,22,22,18,18,18,18,18,18,22,22,22,22,...
        NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA,NA];
    Ncs_check = restricted_set_Ncs_check_table(logic_root_index+1);
    if Ncs~=Ncs_check
        error('zeroCorrelationZoneConfig(higher-layer parameter) does not match logic root index');
    end
    Ncs = Ncs_check;
end
PRACH_para.Ncs = Ncs;

%% calculate du Cv etc.
[PRACH_para.du, PRACH_para.Cv] = fn_PRACH_Cv_calculator(PRACH_para.L_RA, phy_root_index_u, restricted_set_cfg, PRACH_para.Ncs, logic_root_index);

%% generate preamble pool for current cell
Cv_length           = length(PRACH_para.Cv);
PRACH_para.n_root   = ceil(64/Cv_length);

switch PRACH_para.L_RA
    case 839
        PRACH_para.phy_root_index_u_list = logic_root_index_mapping_table_839([(logic_root_index + 1):(logic_root_index+1 + PRACH_para.n_root - 1)]);
    case 139
        PRACH_para.phy_root_index_u_list = logic_root_index_mapping_table_139([(logic_root_index + 1):(logic_root_index+1 + PRACH_para.n_root - 1)]);
    otherwise
        error('no such preable length');
end

counter = 0;
preamble_pool_time_domain        = zeros(preamble_num_per_cell, PRACH_para.L_RA);
preamble_pool_freq_domain        = zeros(preamble_num_per_cell, PRACH_para.L_RA);
preamble_local_freq_domain       = zeros(PRACH_para.n_root, PRACH_para.L_RA);
for mm = 0:1:(PRACH_para.n_root-1)
    ZC_seq_basic                        = fn_ZC_seq_generator(PRACH_para.L_RA, PRACH_para.phy_root_index_u_list(mm+1));
    
    for nn = 0:1:(Cv_length-1)
        counter                                 = counter+1;
        Cv_current                              = PRACH_para.Cv(nn+1);
        ZC_seq_shift                            = circshift(ZC_seq_basic.', -Cv_current).';
        preamble_pool_time_domain(counter,:)    = ZC_seq_shift;
        preamble_pool_freq_domain(counter,:)    = (1/sqrt(length(ZC_seq_shift)))*fft(ZC_seq_shift);
        
        if nn == 0
            preamble_local_freq_domain(mm+1,:)  = conj(preamble_pool_freq_domain(counter,:));
        end
        
        if counter>preamble_num_per_cell
            break;
        end
    end
    
end

PRACH_para.preamble_pool_time_domain    = preamble_pool_time_domain;
PRACH_para.preamble_pool_freq_domain    = preamble_pool_freq_domain;
PRACH_para.preamble_local_freq_domain   = preamble_local_freq_domain;

%% detecting threshold
if strcmp(PRACH_format, '0')
    switch restricted_set_cfg
        case 0
            % Ncx/64ant/16ant/8ant
            detecting_threshold_table = [...
                000 4.89 6.53 7.9;
                013 4.97 6.67 8.2;
                015 4.94 6.61 7.9;
                018 4.90 6.54 8.2;
                022 4.89 6.46 7.9;
                026 4.87 6.42 7.8;
                032 4.84 6.37 7.7;
                038 2.53 6.33 8.2;
                046 4.87 6.37 8.1;
                059 4.86 6.36 8.1;
                076 4.85 6.50 7.9;
                093 4.85 6.48 7.8;
                119 4.84 6.46 7.8;
                167 4.91 6.44 7.8;
                279 4.90 6.49 7.9;
                419 4.90 6.54 7.9];
        case 1
            % Ncx/64ant/16ant/8ant
            detecting_threshold_table = [...
                015 NA 6.73 NA;
                018 NA 6.67 NA;
                022 NA 6.74 NA;
                026 NA 6.74 NA;
                032 NA 6.72 NA
                038 NA 6.70 NA;
                046 NA 6.64 NA;
                055 NA 6.76 NA;
                068 NA 6.72 NA;
                082 NA 6.73 NA
                100 NA 6.69 NA;
                128 NA 6.70 NA;
                158 NA 6.51 NA;
                202 NA 6.66 NA;
                237 NA 6.66 NA];
    end
end

if strcmp(PRACH_format, '1')
    switch restricted_set_cfg
        case 0
            % Ncx/64ant/16ant/8ant
            detecting_threshold_table = [...
                000 4.39 5.63 NA;
                013 4.38 5.67 NA;
                015 4.36 5.52 NA;
                018 4.33 5.58 NA;
                022 4.31 5.54 NA;
                026 4.31 5.50 NA;
                032 4.30 5.47 NA;
                038 2.31 5.45 NA;
                046 4.31 5.42 NA;
                059 4.31 5.44 NA;
                076 4.30 5.47 NA;
                093 4.30 5.45 NA;
                119 4.29 5.59 NA;
                167 4.36 5.58 NA;
                279 4.35 5.58 NA;
                419 4.35 5.58 NA];
        case 1
            
    end
end

if strcmp(PRACH_format, '2')
    
end

if strcmp(PRACH_format, '3')
    
end

if strcmp(PRACH_format, 'A1')
    
end

if strcmp(PRACH_format, 'A2')
    
end

if strcmp(PRACH_format, 'A3')
    
end

if strcmp(PRACH_format, 'B1')
    
end

if strcmp(PRACH_format, 'B2')
    
end

if strcmp(PRACH_format, 'B3')
    
end

if strcmp(PRACH_format, 'B4')
    
end

if strcmp(PRACH_format, 'C0')
    
end

if strcmp(PRACH_format, 'C1')
    
end

switch RX_num
    case 64
        col_idx = 1;
    case 16
        col_idx = 2;
    case 8
        col_idx = 3;
end
row_idx = find(detecting_threshold_table(:,1) == Ncs);
detecting_threshold = detecting_threshold_table(row_idx, col_idx+1);
detecting_threshold = 10^(detecting_threshold/10);
PRACH_para.detecting_threshold = detecting_threshold;

end
