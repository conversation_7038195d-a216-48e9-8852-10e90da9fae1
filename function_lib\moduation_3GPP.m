function modulated_symbol = moduation_3GPP(input, modulation_mode)
bits_num = length(input);

GAIN_QPSK4 = 1/sqrt(2);

%% constellation for QPSK
real_QPSK = [1,  1, -1, -1];
imag_QPSK = [1, -1,  1, -1];


switch modulation_mode
    case 1

    case 2
    symbol_num = fix(bits_num/2);
    symbol_index = 1:1:symbol_num;
    symbol = input(1, 2*symbol_index-1)*2 + input(1, 2*symbol_index);
    C_re = real_QPSK(symbol+1)*GAIN_QPSK4;
    C_im = imag_QPSK(symbol+1)*GAIN_QPSK4;
    modulated_symbol = C_re + 1j*C_im;
end