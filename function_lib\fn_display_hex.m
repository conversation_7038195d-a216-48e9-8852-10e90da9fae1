function output = fn_display_hex(input_original, complement_flag, display_mode)
if isfi(input_original)
    fractionLength = input_original.FractionLength;
    input_real_fix = real(input_original);
    int_real = floor(double(input_real_fix)*(2^fractionLength));
    
    input_imag_fix = imag(input_original);
    int_imag = floor(double(input_imag_fix)*(2^fractionLength));
    
    word_len = input_real_fix.WordLength;
else
    int_real = real(input_original);
    int_imag = imag(input_original);    
end


if complement_flag == 1
    %% complement
    int_real(int_real<0) = (2^word_len) - abs(int_real(int_real<0));
    int_imag(int_imag<0) = (2^word_len) - abs(int_imag(int_imag<0));
end

switch display_mode
    case 0
        data_tmp = int_real*(2^word_len) + int_imag;
        output   = dec2hex(data_tmp, 2*word_len/4);
    case 1
        output = [dec2hex(int_real),dec2hex(int_imag)];
    case 2
        output = dec2hex(int_real);
end


