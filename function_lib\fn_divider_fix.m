function output = fn_divider_fix(numerator, denominator, sign_var_flag , output_word_len, output_fraction_len, intermediate_var_max_word_len)
% sign_var_flag                  - 1-sign var process 0-unsign var process
% intermediate_var_max_word_len  - max bits number of intermediate variable in divider
if nargin < 6
    intermediate_var_max_word_len = 64;
end

int_X                   = double(numerator)*(2^numerator.FractionLength);
int_Y                   = double(denominator)*(2^denominator.FractionLength);

if int_Y == 0
    int_Y = 1;
end

if int_X ~= 0
    division_max_word_len   = max([numerator.WordLength, denominator.WordLength, intermediate_var_max_word_len]);
    
    if int_X>=0
        X_shift_bits_num_A      = division_max_word_len - sign_var_flag - ceil(log2(int_X+1));
    else
        X_shift_bits_num_A      = division_max_word_len - sign_var_flag - ceil(log2(abs(int_X)));
    end
    
    if int_Y>=0
        Y_shift_bits_num_A      = division_max_word_len - sign_var_flag - ceil(log2(int_Y+1)) - (output_word_len - sign_var_flag - 1);
    else
        Y_shift_bits_num_A      = division_max_word_len - sign_var_flag - ceil(log2(abs(int_Y))) - (output_word_len - sign_var_flag - 1);
    end

    int_X_shift_A           = fix(int_X*(2^X_shift_bits_num_A));
    int_Y_shift_A           = fix(int_Y*(2^Y_shift_bits_num_A));
    
    int_X_Y                 = fix(int_X_shift_A/int_Y_shift_A);
    
    total_shift_bits_num    = numerator.FractionLength + X_shift_bits_num_A - (denominator.FractionLength + Y_shift_bits_num_A) - output_fraction_len;
    
    int_X_Y_shift           = fix(int_X_Y*(2^(-total_shift_bits_num)));
    
    output                  = fi(int_X_Y_shift/(2^output_fraction_len), numerictype(sign_var_flag, output_word_len, output_fraction_len), numerator.fimath);
else
    output                  = fi(0, numerictype(sign_var_flag, output_word_len, output_fraction_len), numerator.fimath);
end




end