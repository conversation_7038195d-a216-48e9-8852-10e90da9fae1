function [error_real, error_imag, max_error_value, max_error_bit_num, mean_error_value, mean_error_bit_num] = fn_error_cal(ref, data, figure_enable, variable_name)

ref_real    = real(ref);
ref_imag    = imag(ref);
data_real   = real(data);
data_imag   = imag(data);

error_real = ref_real - data_real;
error_imag = ref_imag - data_imag;

max_error_value = max([abs(error_real), abs(error_imag)]);
max_error_bit_num = ceil(log2(max_error_value));

mean_error_value = mean([abs(error_real), abs(error_imag)]);
mean_error_bit_num = ceil(log2(mean_error_value));

if figure_enable
    figure
    ref_tag = strcat(variable_name, ' Ref Real Part');
    data_tag = strcat(variable_name, ' Data Real Part');
    plot(ref_real);
    hold on;
    grid on;
    plot(data_real,'r');
    legend(ref_tag,data_tag);
    title('Real Part');
    
    figure
    ref_tag = strcat(variable_name, ' Ref Imaginary Part');
    data_tag = strcat(variable_name, ' Data Imaginary Part');
    plot(ref_imag);
    hold on;
    grid on;
    plot(data_imag,'r');
    legend(ref_tag,data_tag);
    title('Imaginary Part');
    
    figure
    ref_tag = strcat(variable_name, ' Error of Real Part');
    data_tag = strcat(variable_name, ' Error of Imaginary Part');
    plot(error_real);
    hold on;
    grid on;
    plot(error_imag,'r');
    legend(ref_tag,data_tag);
    title('Error');
end