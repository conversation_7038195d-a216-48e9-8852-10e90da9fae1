function output = fn_CordicInAC(input)

input_complex = floor(double(input)*(2^input.FractionLength));

for kk = 1:1:length(input_complex)
    input_current = input_complex(kk);
    input_current_real = real(input_current);
    input_current_imag = imag(input_current);
    
    output(kk) = CordicInACForMatlab(input_current_real, input_current_imag);
    
end

output = fi(output/(2^29), numerictype(1, 32, 29), fimath('RoundMode','round','OverflowMode','Saturate'));