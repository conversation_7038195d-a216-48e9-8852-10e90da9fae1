%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% COPYRIGHT
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% File name:    fn_combine_PDP_fix.m
%
% Function:     combine PDP over antenna and repetition
%               
%
% Input:        correlation_result_time_domain_all_fix - correlation result
%               AGC_TD_list                            - AGC factor of time domain
%               fft_exponent_list                      - exponent of xilinx FFT C module
%               ifft_exponent_list                     - exponent of xilinx IFFT C module
%
% Output:       PDP_matrix_fix                         - PDP
%
% Modification History
% Date      20180125
% Author    ZONG Yaozong
% Content   Create file
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
function PDP_matrix_fix = fn_combine_PDP_fix(correlation_result_time_domain_all_fix, AGC_TD_list, fft_exponent_list, ifft_exponent_list)
fn_fix_point_format_table;

[seq_rep_num, basic_seq_num, rx_num, correlation_result_len] = size(correlation_result_time_domain_all_fix);

PDP_matrix_fix = fi(zeros(basic_seq_num, correlation_result_len), SW32F30, F_floor);

for basic_seq_idx = 0:1:(basic_seq_num-1)
        
    %% AGC shift target
    AGC_total_current_basic_seq     = zeros(seq_rep_num, rx_num);
    for seq_rep_idx = 0:1:(seq_rep_num-1)
        AGC_TD_current_basic_seq        = AGC_TD_list(seq_rep_idx+1, :);
        fft_exponent_current_basic_seq  = fft_exponent_list(seq_rep_idx+1, :);
        ifft_exponent_current_basic_seq = squeeze(ifft_exponent_list(seq_rep_idx+1, basic_seq_idx+1, :));
        if (size(ifft_exponent_current_basic_seq, 2) == 1)
            ifft_exponent_current_basic_seq = ifft_exponent_current_basic_seq.';
        end
        AGC_total_current_basic_seq(seq_rep_idx+1, :) = AGC_TD_current_basic_seq + fft_exponent_current_basic_seq + ifft_exponent_current_basic_seq; 
    end % sequence repetition loop
    AGC_total_ref       = min(min(AGC_total_current_basic_seq));
    
    PDP_sum_result_current_basic_seq_fix  = fi(zeros(1, correlation_result_len), SW64F30, F_floor); 
    
    for seq_rep_idx = 0:1:(seq_rep_num-1)
        
        %% read correlation result
        correlation_result_current_tmp  = correlation_result_time_domain_all_fix(seq_rep_idx+1, basic_seq_idx+1, : ,:);
        correlation_result_current_tmp  = squeeze(correlation_result_current_tmp);
        [row_num, col_num]              = size(correlation_result_current_tmp);
        if (col_num~=correlation_result_len)
            correlation_result_current_tmp = correlation_result_current_tmp.';
        end
        correlation_result_current_fix  = fi(correlation_result_current_tmp, SW16F15, F_floor);
        
        %% AGC shift        
        for rx_idx = 0:1:(rx_num-1)
            AGC_total_current_rx                                = AGC_TD_list(seq_rep_idx+1, rx_idx+1) + fft_exponent_list(seq_rep_idx+1, rx_idx+1) ... 
                                                                  + ifft_exponent_list(seq_rep_idx+1, basic_seq_idx+1, rx_idx+1);
            AGC_shift_current_rx                                = AGC_total_ref - AGC_total_current_rx;
            correlation_result_shift_fix                        = fi(correlation_result_current_fix(rx_idx+1,:)*(2^AGC_shift_current_rx), SW16F15, F_floor);
            correlation_result_shift_power_tmp                  = fi(correlation_result_shift_fix.*conj(correlation_result_shift_fix), SW32F30, F_floor);
            % correlation_result_shift_power_fix(seq_rep_idx+1, rx_idx+1, :)     = correlation_result_shift_power_tmp;
            
            PDP_sum_result_current_basic_seq_fix                = fi(PDP_sum_result_current_basic_seq_fix + correlation_result_shift_power_tmp, SW64F30, F_floor);
        end % rx loop
        
    end % sequence repetition loop
    
    accumulation_num  = rx_num*seq_rep_num;
    avg_shift_bit_num = log2(accumulation_num);
    PDP_current_basic_seq_fix = fi(bitshift(PDP_sum_result_current_basic_seq_fix, -avg_shift_bit_num), SW32F30, F_floor);
    PDP_matrix_fix(basic_seq_idx+1 ,:) = PDP_current_basic_seq_fix;
    
end % basic sequence loop

end